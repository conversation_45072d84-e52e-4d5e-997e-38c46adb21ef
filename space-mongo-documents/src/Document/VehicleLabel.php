<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;

#[MongoDB\Document(collection: 'vehicleLabel', repositoryClass: 'Space\MongoDocuments\Repository\VehicleLabelRepository')]
class VehicleLabel
{
    #[MongoDB\Id]
    private ?string $id = null;

    #[MongoDB\Field(type: 'collection')]
    private array $lcdv = [];

    #[MongoDB\Field(type: 'string')]
    private ?string $label = null;

    #[MongoDB\Field(type: 'bool')]
    private ?bool $isO2X = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $sdp = null;

    // Getters and Setters

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getLcdv(): array
    {
        return $this->lcdv;
    }

    public function setLcdv(array $lcdv): self
    {
        $this->lcdv = $lcdv;
        return $this;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(?string $label): self
    {
        $this->label = $label;
        return $this;
    }

    public function getIsO2X(): ?bool
    {
        return $this->isO2X;
    }

    public function setIsO2X(?bool $isO2X): self
    {
        $this->isO2X = $isO2X;
        return $this;
    }

    public function getSdp(): ?string
    {
        return $this->sdp;
    }

    public function setSdp(?string $sdp): self
    {
        $this->sdp = $sdp;
        return $this;
    }
}
