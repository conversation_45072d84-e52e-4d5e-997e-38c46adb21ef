<?php

namespace App\Service;

use App\Helper\RefreshVehicleHelper;
use App\Model\SystemVehicleData;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\Vehicle;
use App\Helper\BrandProvider;

/**
 * VehicleTransformationService handles vehicle data transformation and formatting
 * Extracted from VehicleManager to improve maintainability and separation of concerns
 */
class VehicleTransformationService
{
    use LoggerTrait;

    public function __construct(
        private VehicleValidationService $validationService,
        private VehicleOrderService $vehicleOrderService,
        private UserDataService $userDataService
    ) {
    }

    /**
     * Transform Vehicle ODM document to API response format
     */
    public function transformVehicleToApiResponse(Vehicle $vehicle, string $country = 'IT', string $language = 'it'): array
    {
        $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Transforming vehicle to API response', [
            'vin' => $vehicle->getVin(),
            'country' => $country,
            'language' => $language,
        ]);

        $response = [
            'id' => $vehicle->getDocumentId() ?? RefreshVehicleHelper::generateUid(),
            'vin' => $vehicle->getVin(),
            'label' => $vehicle->getLabel() ?? '',
            'versionId' => $vehicle->getVersionId() ?? '',
            'brand' => $vehicle->getBrand() ?? '',
            'visual' => $this->getVehicleVisual($vehicle),
            'language' => $language,
            'country' => $country,
            'isOrder' => $this->validationService->isVehicleOnOrder($vehicle),
        ];

        // Add vehicle order information if available
        $vehicleOrder = $this->extractVehicleOrderInfo($vehicle);
        if (!empty($vehicleOrder)) {
            $response['vehicleOrder'] = $vehicleOrder;
        }

        // Add optional fields if available
        if ($vehicle->getNickName()) {
            $response['nickName'] = $vehicle->getNickName();
        }

        if ($vehicle->getPicture()) {
            $response['picture'] = $vehicle->getPicture();
        }

        if ($vehicle->getType()) {
            $response['type'] = $vehicle->getType();
        }

        if ($vehicle->getModel()) {
            $response['model'] = $vehicle->getModel();
        }

        if ($vehicle->getYear()) {
            $response['year'] = $vehicle->getYear();
        }

        return $response;
    }

    /**
     * Transform multiple vehicles to API response format
     */
    public function transformVehiclesToApiResponse(array $vehicles, string $country = 'IT', string $language = 'it'): array
    {
        $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Transforming vehicles to API response', [
            'vehicleCount' => count($vehicles),
            'country' => $country,
            'language' => $language,
        ]);

        $transformedVehicles = [];
        foreach ($vehicles as $vehicle) {
            if ($vehicle instanceof Vehicle) {
                $transformedVehicles[] = $this->transformVehicleToApiResponse($vehicle, $country, $language);
            }
        }

        return $transformedVehicles;
    }

    /**
     * Extract vehicle order information
     */
    public function extractVehicleOrderInfo(Vehicle $vehicle): array
    {
        $orderInfo = [];

        // Extract order information from feature codes or other fields
        $featureCodes = $vehicle->getFeatureCode() ?? [];
        
        foreach ($featureCodes as $featureCode) {
            if (is_array($featureCode)) {
                // Look for order-related feature codes
                if (isset($featureCode['code']) && in_array($featureCode['code'], ['ORDER_STATUS', 'DELIVERY_STATUS'])) {
                    $orderInfo['status'] = $featureCode['value'] ?? 'UNKNOWN';
                }
                
                if (isset($featureCode['code']) && $featureCode['code'] === 'ORDER_DATE') {
                    $orderInfo['orderDate'] = $featureCode['value'] ?? null;
                }
                
                if (isset($featureCode['code']) && $featureCode['code'] === 'DELIVERY_DATE') {
                    $orderInfo['deliveryDate'] = $featureCode['value'] ?? null;
                }
            }
        }

        // Add default order information if vehicle is on order but no specific info found
        if ($this->validationService->isVehicleOnOrder($vehicle) && empty($orderInfo)) {
            $orderInfo = [
                'status' => $vehicle->getStatus() ?? 'ORDERED',
                'orderDate' => null,
                'deliveryDate' => null,
            ];
        }

        return $orderInfo;
    }

    /**
     * Get vehicle visual information
     */
    public function getVehicleVisual(Vehicle $vehicle): array
    {
        $visual = [
            'picture' => $vehicle->getPicture() ?? '',
            'color' => '',
            'angle' => 'front',
        ];

        // Extract color information from feature codes if available
        $featureCodes = $vehicle->getFeatureCode() ?? [];
        foreach ($featureCodes as $featureCode) {
            if (is_array($featureCode) && isset($featureCode['code']) && $featureCode['code'] === 'COLOR') {
                $visual['color'] = $featureCode['value'] ?? '';
                break;
            }
        }

        return $visual;
    }

    /**
     * Transform SystemVehicleData to simplified array format
     */
    public function transformSystemVehicleDataToArray(SystemVehicleData $systemVehicleData): array
    {
        return [
            'vin' => $systemVehicleData->getVin(),
            'brand' => $systemVehicleData->getBrandCode(),
            'label' => $systemVehicleData->getLabel(),
            'versionId' => $systemVehicleData->getLcdv(),
            'type' => $systemVehicleData->getType(),
            'make' => $systemVehicleData->getMake(),
            'year' => $systemVehicleData->getYear(),
            'nickname' => $systemVehicleData->getNickname(),
            'imageUrl' => $systemVehicleData->getImageUrl(),
            'market' => $systemVehicleData->getMarket(),
            'enrollmentStatus' => $systemVehicleData->getEnrollmentStatus(),
            'connectorType' => $systemVehicleData->getConnectorType(),
            'lastUpdate' => $systemVehicleData->getLastUpdate(),
            'warrantyStartDate' => $systemVehicleData->getWarrantyStartDate(),
        ];
    }

    /**
     * Transform vehicle for feature testing response
     */
    public function transformVehicleForFeatureTesting(Vehicle $vehicle, array $featureCodes = []): array
    {
        $response = [
            'vin' => $vehicle->getVin(),
            'brand' => $vehicle->getBrand(),
            'model' => $vehicle->getModel() ?? $vehicle->getLabel(),
            'versionId' => $vehicle->getVersionId(),
            'status' => $vehicle->getStatus() ?? 'DELIVERED',
            'featureCodes' => $featureCodes,
        ];

        // Add optional fields
        if ($vehicle->getType()) {
            $response['type'] = $vehicle->getType();
        }

        if ($vehicle->getYear()) {
            $response['year'] = $vehicle->getYear();
        }

        return $response;
    }

    /**
     * Transform feature codes for API response
     */
    public function transformFeatureCodes(array $featureCodes): array
    {
        $transformedCodes = [];

        foreach ($featureCodes as $featureCode) {
            if (is_array($featureCode)) {
                $transformedCodes[] = [
                    'code' => $featureCode['code'] ?? '',
                    'status' => $featureCode['status'] ?? 'unknown',
                    'value' => $featureCode['value'] ?? null,
                ];
            } elseif (is_string($featureCode)) {
                $transformedCodes[] = [
                    'code' => $featureCode,
                    'status' => 'enabled',
                    'value' => null,
                ];
            }
        }

        return $transformedCodes;
    }

    /**
     * Extract criteria value from vehicle data
     */
    public function extractCriteriaValue(Vehicle $vehicle): ?string
    {
        // Look for criteria value in feature codes
        $featureCodes = $vehicle->getFeatureCode() ?? [];
        
        foreach ($featureCodes as $featureCode) {
            if (is_array($featureCode) && isset($featureCode['code']) && $featureCode['code'] === 'CRITERIA') {
                return $featureCode['value'] ?? null;
            }
        }

        // Fallback to version ID or other identifier
        return $vehicle->getVersionId();
    }

    /**
     * Format vehicle summary for API response
     */
    public function formatVehicleSummary(array $vehicles, string $userId): array
    {
        $summary = [
            'userId' => $userId,
            'totalVehicles' => count($vehicles),
            'vehicles' => [],
            'brands' => [],
        ];

        $brandCounts = [];

        foreach ($vehicles as $vehicle) {
            if ($vehicle instanceof Vehicle) {
                $vehicleData = $this->transformVehicleToApiResponse($vehicle);
                $summary['vehicles'][] = $vehicleData;

                // Count brands
                $brand = $vehicle->getBrand();
                if ($brand) {
                    $brandCounts[$brand] = ($brandCounts[$brand] ?? 0) + 1;
                }
            }
        }

        // Format brand summary
        foreach ($brandCounts as $brand => $count) {
            $summary['brands'][] = [
                'brand' => $brand,
                'count' => $count,
            ];
        }

        return $summary;
    }

    /**
     * Generate unique vehicle identifier
     */
    public function generateVehicleId(): string
    {
        return RefreshVehicleHelper::generateUid();
    }

    /**
     * Convert ODM Vehicle to the exact API response format expected by v1/vehicles endpoint
     */
    public function convertODMVehicleToApiFormat(Vehicle $vehicle, string $userId = ''): array
    {
        // Ensure all required fields have non-null values
        $vin = $vehicle->getVin() ?? '';
        $brand = $vehicle->getBrand() ?? 'UNKNOWN';
        $model = $vehicle->getModel() ?? '';
        $versionId = $vehicle->getVersionId() ?? '';
        $label = $vehicle->getLabel() ?? $model;

        // Extract order information from the vehicle
        $orderInfo = $this->vehicleOrderService->extractOrderInformationFromVehicle($vehicle, $userId);

        // Extract additional vehicle data from raw MongoDB document
        $additionalVehicleData = $this->extractAdditionalVehicleData($vehicle, $userId);

        // Build vehicleOrder object in the expected format
        $vehicleOrder = [
            'mopId' => $orderInfo['mopId'] ?? '',
            'orderFormId' => $orderInfo['orderFormId'] ?? '',
            'trackingStatus' => $orderInfo['trackingStatus'] ?? '',
            'isUpdated' => false, // Default value for backward compatibility
            'orderFormStatus' => $orderInfo['orderFormStatus'] ?? '',
        ];

        // Parse language and country from culture if available
        $language = null;
        $country = null;
        if (!empty($additionalVehicleData['culture'])) {
            $cultureParts = explode('-', $additionalVehicleData['culture']);
            if (count($cultureParts) >= 2) {
                $language = $cultureParts[0];
                $country = $cultureParts[1];
            }
        }

        // Use language from vehicle data if available, otherwise from culture
        $language = $additionalVehicleData['language'] ?? $language;

        // Use country from vehicle data if available, otherwise from culture
        $country = $additionalVehicleData['country'] ?? $country;

        // Use actual vehicle picture if available, otherwise fallback to brand default
        $visual = $additionalVehicleData['picture'] ?? BrandProvider::getBrandDefaultImage($brand);

        // Get the document ID for the vehicle
        $documentId = $vehicle->getDocumentId();

        // If no documentId is set, generate one and log a warning
        if (empty($documentId)) {
            $documentId = $this->generateVehicleId();
            // Note: We should save this back to the database, but for now just use it in response
        }

        // Return the exact format expected by the v1/vehicles API
        return [
            'id' => $documentId, // Using documentId as ID for consistency with v2 API
            'vin' => $vin,
            'label' => $label,
            'versionId' => $versionId,
            'brand' => $brand,
            'visual' => $visual,
            'language' => $language,
            'country' => $country,
            'isOrder' => $this->vehicleOrderService->isVehicleOnOrder($vehicle),
            'vehicleOrder' => $vehicleOrder,
        ];
    }

    /**
     * Extract additional vehicle data from raw MongoDB document that's not available in ODM Vehicle
     */
    private function extractAdditionalVehicleData(Vehicle $vehicle, string $userId): array
    {
        $additionalData = [
            'country' => null,
            'language' => null,
            'culture' => null,
            'picture' => null,
        ];

        try {
            // Get raw user data to access fields not mapped in ODM
            $rawUserData = $this->userDataService->getRawUserData($userId);
            if ($rawUserData && isset($rawUserData['vehicle'])) {
                foreach ($rawUserData['vehicle'] as $vehicleData) {
                    // Handle both array and Vehicle object cases
                    $vehicleVin = null;
                    if (is_array($vehicleData)) {
                        $vehicleVin = $vehicleData['vin'] ?? null;
                    } elseif ($vehicleData instanceof Vehicle) {
                        $vehicleVin = $vehicleData->getVin();
                    }

                    if ($vehicleVin && $vehicleVin === $vehicle->getVin()) {
                        if (is_array($vehicleData)) {
                            $additionalData['country'] = $vehicleData['country'] ?? null;
                            $additionalData['language'] = $vehicleData['language'] ?? null;
                            $additionalData['culture'] = $vehicleData['culture'] ?? null;
                            $additionalData['picture'] = $vehicleData['picture'] ?? null;
                        } elseif ($vehicleData instanceof Vehicle) {
                            $additionalData['country'] = 'AT'; // Default country since ODM Vehicle doesn't store country
                            $additionalData['language'] = 'en'; // Default language since ODM Vehicle doesn't store language
                            $additionalData['culture'] = 'en-AT'; // Default culture
                            $additionalData['picture'] = $vehicleData->getPicture();
                        }
                        break;
                    }
                }
            }
        } catch (\Exception $e) {
            // Log error but continue with default values
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error extracting additional vehicle data', [
                'userId' => $userId,
                'vehicleVin' => $vehicle->getVin(),
                'error' => $e->getMessage(),
            ]);
        }

        return $additionalData;
    }
}
