<?php

namespace App\Service;

use App\Helper\RefreshVehicleHelper;
use App\Model\SystemVehicleData;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\Vehicle;

/**
 * VehicleTransformationService handles vehicle data transformation and formatting
 * Extracted from VehicleManager to improve maintainability and separation of concerns
 */
class VehicleTransformationService
{
    use LoggerTrait;

    public function __construct(
        private VehicleValidationService $validationService
    ) {
    }

    /**
     * Transform Vehicle ODM document to API response format
     */
    public function transformVehicleToApiResponse(Vehicle $vehicle, string $country = 'IT', string $language = 'it'): array
    {
        $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Transforming vehicle to API response', [
            'vin' => $vehicle->getVin(),
            'country' => $country,
            'language' => $language,
        ]);

        $response = [
            'id' => $vehicle->getDocumentId() ?? RefreshVehicleHelper::generateUid(),
            'vin' => $vehicle->getVin(),
            'label' => $vehicle->getLabel() ?? '',
            'versionId' => $vehicle->getVersionId() ?? '',
            'brand' => $vehicle->getBrand() ?? '',
            'visual' => $this->getVehicleVisual($vehicle),
            'language' => $language,
            'country' => $country,
            'isOrder' => $this->validationService->isVehicleOnOrder($vehicle),
        ];

        // Add vehicle order information if available
        $vehicleOrder = $this->extractVehicleOrderInfo($vehicle);
        if (!empty($vehicleOrder)) {
            $response['vehicleOrder'] = $vehicleOrder;
        }

        // Add optional fields if available
        if ($vehicle->getNickName()) {
            $response['nickName'] = $vehicle->getNickName();
        }

        if ($vehicle->getPicture()) {
            $response['picture'] = $vehicle->getPicture();
        }

        if ($vehicle->getType()) {
            $response['type'] = $vehicle->getType();
        }

        if ($vehicle->getModel()) {
            $response['model'] = $vehicle->getModel();
        }

        if ($vehicle->getYear()) {
            $response['year'] = $vehicle->getYear();
        }

        return $response;
    }

    /**
     * Transform multiple vehicles to API response format
     */
    public function transformVehiclesToApiResponse(array $vehicles, string $country = 'IT', string $language = 'it'): array
    {
        $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Transforming vehicles to API response', [
            'vehicleCount' => count($vehicles),
            'country' => $country,
            'language' => $language,
        ]);

        $transformedVehicles = [];
        foreach ($vehicles as $vehicle) {
            if ($vehicle instanceof Vehicle) {
                $transformedVehicles[] = $this->transformVehicleToApiResponse($vehicle, $country, $language);
            }
        }

        return $transformedVehicles;
    }

    /**
     * Extract vehicle order information
     */
    public function extractVehicleOrderInfo(Vehicle $vehicle): array
    {
        $orderInfo = [];

        // Extract order information from feature codes or other fields
        $featureCodes = $vehicle->getFeatureCode() ?? [];
        
        foreach ($featureCodes as $featureCode) {
            if (is_array($featureCode)) {
                // Look for order-related feature codes
                if (isset($featureCode['code']) && in_array($featureCode['code'], ['ORDER_STATUS', 'DELIVERY_STATUS'])) {
                    $orderInfo['status'] = $featureCode['value'] ?? 'UNKNOWN';
                }
                
                if (isset($featureCode['code']) && $featureCode['code'] === 'ORDER_DATE') {
                    $orderInfo['orderDate'] = $featureCode['value'] ?? null;
                }
                
                if (isset($featureCode['code']) && $featureCode['code'] === 'DELIVERY_DATE') {
                    $orderInfo['deliveryDate'] = $featureCode['value'] ?? null;
                }
            }
        }

        // Add default order information if vehicle is on order but no specific info found
        if ($this->validationService->isVehicleOnOrder($vehicle) && empty($orderInfo)) {
            $orderInfo = [
                'status' => $vehicle->getStatus() ?? 'ORDERED',
                'orderDate' => null,
                'deliveryDate' => null,
            ];
        }

        return $orderInfo;
    }

    /**
     * Get vehicle visual information
     */
    public function getVehicleVisual(Vehicle $vehicle): array
    {
        $visual = [
            'picture' => $vehicle->getPicture() ?? '',
            'color' => '',
            'angle' => 'front',
        ];

        // Extract color information from feature codes if available
        $featureCodes = $vehicle->getFeatureCode() ?? [];
        foreach ($featureCodes as $featureCode) {
            if (is_array($featureCode) && isset($featureCode['code']) && $featureCode['code'] === 'COLOR') {
                $visual['color'] = $featureCode['value'] ?? '';
                break;
            }
        }

        return $visual;
    }

    /**
     * Transform SystemVehicleData to simplified array format
     */
    public function transformSystemVehicleDataToArray(SystemVehicleData $systemVehicleData): array
    {
        return [
            'vin' => $systemVehicleData->getVin(),
            'brand' => $systemVehicleData->getBrandCode(),
            'label' => $systemVehicleData->getLabel(),
            'versionId' => $systemVehicleData->getLcdv(),
            'type' => $systemVehicleData->getType(),
            'make' => $systemVehicleData->getMake(),
            'year' => $systemVehicleData->getYear(),
            'nickname' => $systemVehicleData->getNickname(),
            'imageUrl' => $systemVehicleData->getImageUrl(),
            'market' => $systemVehicleData->getMarket(),
            'enrollmentStatus' => $systemVehicleData->getEnrollmentStatus(),
            'connectorType' => $systemVehicleData->getConnectorType(),
            'lastUpdate' => $systemVehicleData->getLastUpdate(),
            'warrantyStartDate' => $systemVehicleData->getWarrantyStartDate(),
        ];
    }

    /**
     * Transform vehicle for feature testing response
     */
    public function transformVehicleForFeatureTesting(Vehicle $vehicle, array $featureCodes = []): array
    {
        $response = [
            'vin' => $vehicle->getVin(),
            'brand' => $vehicle->getBrand(),
            'model' => $vehicle->getModel() ?? $vehicle->getLabel(),
            'versionId' => $vehicle->getVersionId(),
            'status' => $vehicle->getStatus() ?? 'DELIVERED',
            'featureCodes' => $featureCodes,
        ];

        // Add optional fields
        if ($vehicle->getType()) {
            $response['type'] = $vehicle->getType();
        }

        if ($vehicle->getYear()) {
            $response['year'] = $vehicle->getYear();
        }

        return $response;
    }

    /**
     * Transform feature codes for API response
     */
    public function transformFeatureCodes(array $featureCodes): array
    {
        $transformedCodes = [];

        foreach ($featureCodes as $featureCode) {
            if (is_array($featureCode)) {
                $transformedCodes[] = [
                    'code' => $featureCode['code'] ?? '',
                    'status' => $featureCode['status'] ?? 'unknown',
                    'value' => $featureCode['value'] ?? null,
                ];
            } elseif (is_string($featureCode)) {
                $transformedCodes[] = [
                    'code' => $featureCode,
                    'status' => 'enabled',
                    'value' => null,
                ];
            }
        }

        return $transformedCodes;
    }

    /**
     * Extract criteria value from vehicle data
     */
    public function extractCriteriaValue(Vehicle $vehicle): ?string
    {
        // Look for criteria value in feature codes
        $featureCodes = $vehicle->getFeatureCode() ?? [];
        
        foreach ($featureCodes as $featureCode) {
            if (is_array($featureCode) && isset($featureCode['code']) && $featureCode['code'] === 'CRITERIA') {
                return $featureCode['value'] ?? null;
            }
        }

        // Fallback to version ID or other identifier
        return $vehicle->getVersionId();
    }

    /**
     * Format vehicle summary for API response
     */
    public function formatVehicleSummary(array $vehicles, string $userId): array
    {
        $summary = [
            'userId' => $userId,
            'totalVehicles' => count($vehicles),
            'vehicles' => [],
            'brands' => [],
        ];

        $brandCounts = [];

        foreach ($vehicles as $vehicle) {
            if ($vehicle instanceof Vehicle) {
                $vehicleData = $this->transformVehicleToApiResponse($vehicle);
                $summary['vehicles'][] = $vehicleData;

                // Count brands
                $brand = $vehicle->getBrand();
                if ($brand) {
                    $brandCounts[$brand] = ($brandCounts[$brand] ?? 0) + 1;
                }
            }
        }

        // Format brand summary
        foreach ($brandCounts as $brand => $count) {
            $summary['brands'][] = [
                'brand' => $brand,
                'count' => $count,
            ];
        }

        return $summary;
    }

    /**
     * Generate unique vehicle identifier
     */
    public function generateVehicleId(): string
    {
        return RefreshVehicleHelper::generateUid();
    }
}
