<?php

namespace App\Service;

use App\Trait\LoggerTrait;
use App\Service\CorvetService;

/**
 * Service for handling Corvet API integration and vehicle data retrieval
 */
class VehicleCorvetService
{
    use LoggerTrait;

    public function __construct(
        private CorvetService $corvetService
    ) {
    }

    /**
     * Get Corvet data for a vehicle
     */
    public function getCorvetData(string $vin, string $brand): ?array
    {
        try {
            $this->logger->info(__METHOD__ . ' Getting Corvet data', [
                'vin' => $vin,
                'brand' => $brand
            ]);

            $corvetData = $this->corvetService->getData($vin, $brand);
            
            if (!$corvetData) {
                $this->logger->warning(__METHOD__ . ' No Corvet data found', [
                    'vin' => $vin,
                    'brand' => $brand
                ]);
                return null;
            }

            $this->logger->info(__METHOD__ . ' Corvet data retrieved successfully', [
                'vin' => $vin,
                'brand' => $brand
            ]);

            return $corvetData;

        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Error getting Corvet data', [
                'vin' => $vin,
                'brand' => $brand,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get LCDV from Corvet data
     */
    public function getLcdv(?array $corvetData): ?string
    {
        try {
            if (!$corvetData) {
                return null;
            }

            // $lcdv = $corvetData['VEHICULE']['LCDV'] ?? null;
            $lcdv = $corvetData['VEHICULE']['DONNEES_VEHICULE']['LCDV_BASE'] ?? null;
            
            $this->logger->info(__METHOD__ . ' LCDV extracted from Corvet data', [
                'lcdv' => $lcdv
            ]);

            return $lcdv;

        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Error extracting LCDV from Corvet data', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get VIN attributes from Corvet
     */
    public function getVinAttributes(string $vin, string $brand): array
    {
        try {
            $this->logger->info(__METHOD__ . ' Getting VIN attributes', [
                'vin' => $vin,
                'brand' => $brand
            ]);

            $vehicleData = $this->corvetService->getData($vin, $brand);
            $attributes = $vehicleData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];

            $this->logger->info(__METHOD__ . ' VIN attributes retrieved', [
                'vin' => $vin,
                'brand' => $brand,
                'attributeCount' => count($attributes)
            ]);

            return $attributes;

        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Error getting VIN attributes', [
                'vin' => $vin,
                'brand' => $brand,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get vehicle brand from attributes
     */
    public function getVehicleBrand(?array $attributes = [], ?string $country = null): string
    {
        if (!$attributes) {
            return '';
        }

        foreach ($attributes as $attribute) {
            if ('DZZ' == substr($attribute, 0, 3)) {
                return $attribute;
            }
        }

        // Fallback logic based on country
        if ($country) {
            switch (strtoupper($country)) {
                case 'FR':
                case 'ES':
                case 'IT':
                    return 'PEUGEOT';
                case 'DE':
                case 'AT':
                    return 'OPEL';
                case 'GB':
                    return 'VAUXHALL';
                default:
                    return 'STELLANTIS';
            }
        }

        return '';
    }

    /**
     * Check if vehicle is OV (Opel/Vauxhall) vehicle for GB case
     */
    public function isOVVehicle(string $versionId): bool
    {
        return 'G' == strtoupper(substr($versionId, 1, 1));
    }

    /**
     * Manage OV (Opel/Vauxhall) data for specific cases
     */
    public function manageOVData(array $vehicle): array
    {
        if (!$this->isOVVehicle($vehicle['versionId'] ?? '')) {
            return $vehicle;
        }

        try {
            $this->logger->info(__METHOD__ . ' Managing OV vehicle data', [
                'vin' => $vehicle['vin'] ?? 'unknown',
                'versionId' => $vehicle['versionId'] ?? 'unknown'
            ]);

            $allAttributes = $this->getVinAttributes($vehicle['vin'], 'OP'); /* forcing OP brand */
            $vehicle['brand'] = $this->getVehicleBrand($allAttributes, $vehicle['country'] ?? null);

            $this->logger->info(__METHOD__ . ' OV vehicle data managed successfully', [
                'vin' => $vehicle['vin'] ?? 'unknown',
                'brand' => $vehicle['brand']
            ]);

            return $vehicle;

        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Error managing OV vehicle data', [
                'vin' => $vehicle['vin'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            return $vehicle;
        }
    }

    /**
     * Mock Corvet data for testing purposes
     */
    public function mockCorvetData(): array
    {
        return [
            "ENTETE" => [
                "STATUT" => "OK",
                "MESSAGE" => "Success"
            ],
            "VEHICULE" => [
                "LCDV" => "TEST_LCDV_123",
                "LISTE_ATTRIBUTES_7" => [
                    "ATTRIBUT" => [
                        [
                            "ATTRIBUT_CODE" => "DXD",
                            "ATTRIBUT_VALUE" => "TEST_VALUE"
                        ],
                        [
                            "ATTRIBUT_CODE" => "DZZ",
                            "ATTRIBUT_VALUE" => "PEUGEOT"
                        ]
                    ]
                ]
            ]
        ];
    }
}
