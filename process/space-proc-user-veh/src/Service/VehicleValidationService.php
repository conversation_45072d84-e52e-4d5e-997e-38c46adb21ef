<?php

namespace App\Service;

use App\Helper\BrandProvider;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\Vehicle;

/**
 * VehicleValidationService handles all vehicle validation logic
 * Extracted from VehicleManager to reduce complexity and improve maintainability
 */
class VehicleValidationService
{
    use LoggerTrait;

    public function __construct(
        private BrandProvider $brandProvider
    ) {
    }

    /**
     * Validate VIN format
     */
    public function validateVin(string $vin): bool
    {
        // VIN should be 17 characters long and contain only alphanumeric characters (excluding I, O, Q)
        if (strlen($vin) !== 17) {
            $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Invalid VIN length', [
                'vin' => $vin,
                'length' => strlen($vin),
            ]);
            return false;
        }

        // Check for invalid characters
        if (!preg_match('/^[A-HJ-NPR-Z0-9]{17}$/', $vin)) {
            $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Invalid VIN format', [
                'vin' => $vin,
            ]);
            return false;
        }

        return true;
    }

    /**
     * Validate brand is supported
     */
    public function validateBrand(string $brand): bool
    {
        try {
            $supportedBrands = $this->brandProvider->getSupportedBrands();
            $isValid = in_array(strtoupper($brand), array_map('strtoupper', $supportedBrands));
            
            if (!$isValid) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Unsupported brand', [
                    'brand' => $brand,
                    'supportedBrands' => $supportedBrands,
                ]);
            }
            
            return $isValid;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error validating brand', [
                'brand' => $brand,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Validate SSDP brand
     */
    public function validateSsdpBrand(string $brand): bool
    {
        try {
            return $this->brandProvider->isSsdpBrand($brand);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error validating SSDP brand', [
                'brand' => $brand,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Validate vehicle data completeness
     */
    public function validateVehicleData(Vehicle $vehicle): array
    {
        $errors = [];

        // Required fields validation
        if (empty($vehicle->getVin())) {
            $errors[] = 'VIN is required';
        } elseif (!$this->validateVin($vehicle->getVin())) {
            $errors[] = 'Invalid VIN format';
        }

        if (empty($vehicle->getBrand())) {
            $errors[] = 'Brand is required';
        } elseif (!$this->validateBrand($vehicle->getBrand())) {
            $errors[] = 'Unsupported brand';
        }

        if (empty($vehicle->getVersionId())) {
            $errors[] = 'Version ID (LCDV) is required';
        }

        if (empty($vehicle->getType())) {
            $errors[] = 'Vehicle type is required';
        }

        // Log validation results
        if (!empty($errors)) {
            $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Vehicle validation failed', [
                'vin' => $vehicle->getVin(),
                'errors' => $errors,
            ]);
        } else {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Vehicle validation passed', [
                'vin' => $vehicle->getVin(),
            ]);
        }

        return $errors;
    }

    /**
     * Check if vehicle is on order based on status
     */
    public function isVehicleOnOrder(Vehicle $vehicle): bool
    {
        $onOrderStatuses = ['ORDERED', 'IN_PRODUCTION', 'SHIPPED', 'PENDING'];
        $status = $vehicle->getStatus();

        // If no status is set, consider it as potentially on order for testing
        if (empty($status)) {
            $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Vehicle has no status, considering as on order for testing', [
                'vin' => $vehicle->getVin(),
            ]);
            return true;
        }

        $isOnOrder = in_array($status, $onOrderStatuses);

        // Additional check for ODM vehicles: check feature codes for order indicators
        if (!$isOnOrder && $vehicle instanceof Vehicle) {
            $featureCodes = $vehicle->getFeatureCode() ?? [];
            foreach ($featureCodes as $featureCode) {
                if (is_array($featureCode) && isset($featureCode['code'])) {
                    // Check for order-related feature codes
                    if (in_array($featureCode['code'], ['ORDER_STATUS', 'DELIVERY_STATUS'])) {
                        $isOnOrder = true;
                        break;
                    }
                }
            }
        }

        $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Vehicle order status determined', [
            'vin' => $vehicle->getVin(),
            'status' => $status,
            'isOnOrder' => $isOnOrder,
        ]);

        return $isOnOrder;
    }

    /**
     * Validate user ID format
     */
    public function validateUserId(string $userId): bool
    {
        // User ID should be a valid UUID or similar format
        if (empty($userId)) {
            $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Empty user ID');
            return false;
        }

        // Check for minimum length and basic format
        if (strlen($userId) < 10) {
            $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' User ID too short', [
                'userId' => $userId,
                'length' => strlen($userId),
            ]);
            return false;
        }

        return true;
    }

    /**
     * Validate language code
     */
    public function validateLanguage(string $language): bool
    {
        $supportedLanguages = ['en', 'fr', 'de', 'es', 'it', 'pt', 'nl', 'pl', 'cs', 'sk'];
        $isValid = in_array(strtolower($language), $supportedLanguages);
        
        if (!$isValid) {
            $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Unsupported language', [
                'language' => $language,
                'supportedLanguages' => $supportedLanguages,
            ]);
        }
        
        return $isValid;
    }

    /**
     * Validate country code
     */
    public function validateCountry(string $country): bool
    {
        $supportedCountries = ['FR', 'DE', 'ES', 'IT', 'PT', 'NL', 'PL', 'CZ', 'SK', 'GB', 'US'];
        $isValid = in_array(strtoupper($country), $supportedCountries);
        
        if (!$isValid) {
            $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Unsupported country', [
                'country' => $country,
                'supportedCountries' => $supportedCountries,
            ]);
        }
        
        return $isValid;
    }

    /**
     * Validate LCDV format
     */
    public function validateLcdv(string $lcdv): bool
    {
        // LCDV should be 24 characters long
        if (strlen($lcdv) !== 24) {
            $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Invalid LCDV length', [
                'lcdv' => $lcdv,
                'length' => strlen($lcdv),
            ]);
            return false;
        }

        // Check for alphanumeric format
        if (!preg_match('/^[A-Z0-9]{24}$/', $lcdv)) {
            $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Invalid LCDV format', [
                'lcdv' => $lcdv,
            ]);
            return false;
        }

        return true;
    }

    /**
     * Comprehensive validation for vehicle addition
     */
    public function validateVehicleForAddition(string $userId, string $vin, string $brand, string $country, string $language): array
    {
        $errors = [];

        if (!$this->validateUserId($userId)) {
            $errors[] = 'Invalid user ID';
        }

        if (!$this->validateVin($vin)) {
            $errors[] = 'Invalid VIN';
        }

        if (!$this->validateBrand($brand)) {
            $errors[] = 'Invalid or unsupported brand';
        }

        if (!$this->validateCountry($country)) {
            $errors[] = 'Invalid or unsupported country';
        }

        if (!$this->validateLanguage($language)) {
            $errors[] = 'Invalid or unsupported language';
        }

        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Vehicle addition validation completed', [
            'userId' => $userId,
            'vin' => $vin,
            'brand' => $brand,
            'country' => $country,
            'language' => $language,
            'errorCount' => count($errors),
        ]);

        return $errors;
    }
}
