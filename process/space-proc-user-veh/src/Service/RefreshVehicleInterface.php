<?php

namespace App\Service;

use Space\MongoDocuments\Document\UserData;
use App\Model\SystemVehicleData;

interface RefreshVehicleInterface
{
    public const BRANDS_XF = ['FT', 'FO', 'AH', 'AR', 'CY', 'DG', 'JE', 'LA', 'RM', 'MA'];
    public const BRANDS_XP = ['AC','AP','DS','OP','VX','SP'];

    public const SDP_GSDP = 'GSDP';             // for XF vehicles
    public const SDP_CVMP = 'CVMP';      // for XP vehicles
    public const SDP_SSDP = 'SSDP';             // for J4U vehicles

    /**
     * @return array<SystemVehicleData>
     */
    public function retrieveVehicles(UserData $userDataDocument, string $brand, string $lanugage, string $country): array;
}