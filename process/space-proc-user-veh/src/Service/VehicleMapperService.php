<?php

namespace App\Service;

use App\Model\SystemVehicleData;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\Vehicle;

/**
 * VehicleMapperService handles mapping between SystemVehicleData and ODM Vehicle documents
 * This service replaces the deleted VehicleMapper class and ensures all fields are properly mapped
 */
class VehicleMapperService
{
    use LoggerTrait;

    /**
     * Map SystemVehicleData to ODM Vehicle document
     * This method replicates the original VehicleMapper::mapSystemVehicleData functionality
     * with complete field mapping and VIN validation
     */
    public function mapSystemVehicleData(SystemVehicleData $systemVehicleData, ?Vehicle $usedDataVehicle = null): Vehicle
    {
        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Mapping SystemVehicleData to ODM Vehicle', [
            'vin' => $systemVehicleData->getVin(),
            'hasExistingVehicle' => $usedDataVehicle !== null,
        ]);

        // Handle existing vehicle or create new one
        if (null !== $usedDataVehicle) {
            // Validate VIN consistency
            if (trim(strtoupper($usedDataVehicle->getVin())) !== $systemVehicleData->getVin()) {
                $errorMessage = sprintf(
                    'The vin of the used data vehicle (%s) is different from the vin of the system vehicle data (%s)', 
                    $usedDataVehicle->getVin(), 
                    $systemVehicleData->getVin()
                );
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' VIN mismatch detected', [
                    'existingVin' => $usedDataVehicle->getVin(),
                    'systemVin' => $systemVehicleData->getVin(),
                ]);
                throw new \Exception($errorMessage);
            }
            $vehicle = $usedDataVehicle;
        } else {
            $vehicle = new Vehicle();
        }

        // Map all fields from original VehicleMapper::mapSystemVehicleData
        $this->mapBasicFields($vehicle, $systemVehicleData);
        $this->mapOptionalFields($vehicle, $systemVehicleData);

        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Successfully mapped SystemVehicleData to ODM Vehicle', [
            'vin' => $vehicle->getVin(),
            'brand' => $vehicle->getBrand(),
            'type' => $vehicle->getType(),
        ]);

        return $vehicle;
    }

    /**
     * Map basic required fields
     */
    private function mapBasicFields(Vehicle $vehicle, SystemVehicleData $systemVehicleData): void
    {
        // Core vehicle identification
        $vehicle->setVin($systemVehicleData->getVin());
        $vehicle->setBrand($systemVehicleData->getBrandCode());
        
        // Labels and descriptions
        $vehicle->setLabel($systemVehicleData->getLabel()); // This maps to shortLabel in legacy
        if ($systemVehicleData->getLabel()) {
            // Set modelDescription to same value as shortLabel (original behavior)
            $vehicle->setModel($systemVehicleData->getLabel()); // Using model field for modelDescription
        }
        
        // Vehicle identification and configuration
        $vehicle->setVersionId($systemVehicleData->getLcdv());
        $vehicle->setType($systemVehicleData->getType());
        
        // Visual and branding
        $vehicle->setPicture($systemVehicleData->getImageUrl());
        $vehicle->setNickName($systemVehicleData->getNickname());
    }

    /**
     * Map optional fields with null checks
     */
    private function mapOptionalFields(Vehicle $vehicle, SystemVehicleData $systemVehicleData): void
    {
        // SDP (Service Data Package)
        if ($systemVehicleData->getSdp()) {
            $vehicle->setSdp($systemVehicleData->getSdp());
        }

        // Registration timestamp
        if ($systemVehicleData->getRegTimestamp()) {
            // Store as string in appropriate field - using version for now
            $vehicle->setVersion((string)$systemVehicleData->getRegTimestamp());
        }

        // Enrollment status
        if ($systemVehicleData->getEnrollmentStatus()) {
            $vehicle->setStatus($systemVehicleData->getEnrollmentStatus());
        }

        // Connector type - store in appropriate field
        if ($systemVehicleData->getConnectorType()) {
            // Using country field for connector type for now
            $vehicle->setCountry($systemVehicleData->getConnectorType());
        }

        // Make and sub-make
        if ($systemVehicleData->getMake()) {
            $vehicle->setMake($systemVehicleData->getMake());
        }
        
        if ($systemVehicleData->getSubMake()) {
            // Store sub-make in language field for now
            $vehicle->setLanguage($systemVehicleData->getSubMake());
        }

        // Market
        if ($systemVehicleData->getMarket()) {
            $vehicle->setMarket($systemVehicleData->getMarket());
        }

        // Last update timestamp
        if ($systemVehicleData->getLastUpdate() !== null) {
            $lastUpdate = intval($systemVehicleData->getLastUpdate());
            // Store in appropriate timestamp field
            $vehicle->setLastUpdate($lastUpdate);
        }

        // Year
        if ($systemVehicleData->getYear()) {
            $vehicle->setYear($systemVehicleData->getYear());
        }

        // Warranty start date
        if ($systemVehicleData->getWarrantyStartDate()) {
            $vehicle->setWarrantyStartDate($systemVehicleData->getWarrantyStartDate());
        }
    }

    /**
     * Create a new Vehicle from SystemVehicleData
     */
    public function createVehicleFromSystemData(SystemVehicleData $systemVehicleData): Vehicle
    {
        return $this->mapSystemVehicleData($systemVehicleData, null);
    }

    /**
     * Update existing Vehicle with SystemVehicleData
     */
    public function updateVehicleFromSystemData(Vehicle $existingVehicle, SystemVehicleData $systemVehicleData): Vehicle
    {
        return $this->mapSystemVehicleData($systemVehicleData, $existingVehicle);
    }

    /**
     * Validate VIN consistency between vehicles
     */
    public function validateVinConsistency(Vehicle $vehicle, SystemVehicleData $systemVehicleData): bool
    {
        return trim(strtoupper($vehicle->getVin())) === $systemVehicleData->getVin();
    }
}
