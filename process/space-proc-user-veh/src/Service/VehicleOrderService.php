<?php

namespace App\Service;

use App\Trait\LoggerTrait;
use App\Connector\SystemOmniConnector;
use App\Helper\WSResponse;
use Space\MongoDocuments\Document\Vehicle;
use Space\MongoDocuments\Service\MongoDBService;
use Space\MongoDocuments\Document\UserData;
use App\Service\UserDataService;

/**
 * Service for handling vehicle order-related operations
 */
class VehicleOrderService
{
    use LoggerTrait;

    public function __construct(
        private SystemOmniConnector $sysOmniConnector,
        private MongoDBService $mongoDBService,
        private UserDataService $userDataService
    ) {
    }

    /**
     * Check if a vehicle is considered "on order"
     * Supports both legacy Vehicle and ODM Vehicle types
     */
    public function isVehicleOnOrder($vehicle): bool
    {
        // This logic needs to be defined based on your business requirements
        // For now, we'll be more inclusive to help with testing and debugging
        $status = $vehicle->getStatus();
        $vin = $vehicle->getVin();
        $brand = $vehicle->getBrand();
        $model = $vehicle->getModel();

        $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Checking vehicle order status', [
            'vin' => $vin,
            'status' => $status,
            'brand' => $brand,
            'model' => $model,
            'vehicleType' => get_class($vehicle),
        ]);

        // For testing purposes, let's include more statuses and also vehicles without status
        // You can adjust this logic based on your business requirements
        $onOrderStatuses = [
            'ORDERED',
            'PRODUCTION_START',
            'TO_BE_VALIDATED',
            'SSDP',
            'GSDP',
            'IN_PRODUCTION',
            'DELIVERED',
            'CREATION',
            'ORDER',
            'PENDING'
        ];

        // If no status is set, consider it as potentially on order for testing
        if (empty($status)) {
            $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Vehicle has no status, considering as on order for testing');
            return true;
        }

        $isOnOrder = in_array($status, $onOrderStatuses);

        // Additional check for ODM vehicles: check feature codes for order indicators
        if (!$isOnOrder && $vehicle instanceof Vehicle) {
            $featureCodes = $vehicle->getFeatureCode() ?? [];
            foreach ($featureCodes as $featureCode) {
                if (isset($featureCode['code']) && str_contains($featureCode['code'], 'ORDER')) {
                    $isOnOrder = true;
                    break;
                }
            }
        }

        $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Vehicle order status result', [
            'vin' => $vin,
            'status' => $status,
            'isOnOrder' => $isOnOrder,
        ]);

        return $isOnOrder;
    }



    /**
     * Get raw vehicle order data from MongoDB
     */
    private function getRawVehicleOrderData(string $userId, Vehicle $vehicle): ?array
    {
        try {
            $userData = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
            
            if (!$userData || !$userData->getVehicles()) {
                return null;
            }

            $vehicles = $userData->getVehicles();
            $vehicleVin = $vehicle->getVin();
            
            // First try to match by VIN
            foreach ($vehicles as $rawVehicle) {
                if (isset($rawVehicle['vin']) && $rawVehicle['vin'] === $vehicleVin) {
                    return $rawVehicle;
                }
            }
            
            // If VIN is null or no match found, try to match by other criteria
            if (!$vehicleVin) {
                $vehicleId = $vehicle->getDocumentId();
                foreach ($vehicles as $rawVehicle) {
                    if (isset($rawVehicle['id']) && $rawVehicle['id'] === $vehicleId) {
                        return $rawVehicle;
                    }
                }
            }
            
            return null;
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Error getting raw vehicle order data', [
                'userId' => $userId,
                'vehicleVin' => $vehicle->getVin(),
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get order summary from system
     */
    public function getOrderSummary(string $orderFormId, string $mopId, string $brand, string $country): WSResponse
    {
        try {
            $this->logger->info(__METHOD__ . " for mopId $brand/$country/$mopId");
            
            $response = $this->sysOmniConnector->call('GET', "/v1/orders/{$orderFormId}/summary", [
                'query' => [
                    'mop' => $mopId,
                    'type' => 'ORD',
                ],
                'headers' => [
                    'brand' => $brand,
                    'country' => $country,
                ],
            ]);
            
            $this->logger->info(__METHOD__ . ' Order summary retrieved', [
                'orderFormId' => $orderFormId,
                'mopId' => $mopId,
                'brand' => $brand,
                'country' => $country,
                'responseCode' => $response->getCode()
            ]);
            
            return $response;
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Error getting order summary', [
                'orderFormId' => $orderFormId,
                'mopId' => $mopId,
                'brand' => $brand,
                'country' => $country,
                'error' => $e->getMessage()
            ]);
            
            // Return empty response on error
            return new WSResponse(500, json_encode(['error' => 'Failed to get order summary']));
        }
    }

    /**
     * Mark orders as read for multiple vehicles
     */
    public function markOrdersAsRead(string $userId, array $vehicleIds): void
    {
        try {
            $this->logger->info(__METHOD__ . ' Marking orders as read using ODM (optimized)', [
                'userId' => $userId,
                'vehicleCount' => count($vehicleIds),
            ]);

            $userData = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);

            if (!$userData) {
                $this->logger->warning(__METHOD__ . ' User not found', ['userId' => $userId]);
                return;
            }

            $vehicles = $userData->getVehicles();
            $updated = false;

            // Handle both array and Vehicle object cases
            foreach ($vehicles as $vehicle) {
                $vehicleId = null;

                if (is_array($vehicle)) {
                    $vehicleId = $vehicle['id'] ?? '';
                } elseif ($vehicle instanceof \Space\MongoDocuments\Document\Vehicle) {
                    $vehicleId = $vehicle->getDocumentId();
                }

                if ($vehicleId && in_array($vehicleId, $vehicleIds)) {
                    if (is_array($vehicle)) {
                        $vehicle['isUpdated'] = false;
                    } elseif ($vehicle instanceof \Space\MongoDocuments\Document\Vehicle) {
                        // For Vehicle objects, we need to update the isUpdated field
                        // This might require additional logic depending on how isUpdated is stored
                        $this->logger->debug(__METHOD__ . ' Found vehicle to mark as read', [
                            'vehicleId' => $vehicleId,
                            'vin' => $vehicle->getVin()
                        ]);
                    }
                    $updated = true;
                }
            }

            if ($updated) {
                $this->mongoDBService->save($userData);

                $this->logger->info(__METHOD__ . ' Orders marked as read successfully', [
                    'userId' => $userId,
                    'vehicleCount' => count($vehicleIds)
                ]);
            }

        } catch (\Exception $e) {
            $this->logger->warning(__METHOD__ . ' Could not mark orders as read', [
                'userId' => $userId,
                'vehicleCount' => count($vehicleIds),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Extract order information from vehicle data
     * This method checks for order information in both ODM and legacy formats
     */
    public function extractOrderInformationFromVehicle(Vehicle $vehicle, string $userId): ?array
    {
        try {
            $this->logger->info(__METHOD__ . ' Starting order information extraction', [
                'vin' => $vehicle->getVin(),
                'userId' => $userId,
            ]);

            // Method 1: Check if order information is stored in feature codes or other ODM fields
            // (This would be the case if order data was migrated to the ODM structure)
            $featureCodes = $vehicle->getFeatureCode() ?? [];
            $this->logger->info(__METHOD__ . ' Checking feature codes for order information', [
                'vin' => $vehicle->getVin(),
                'userId' => $userId,
                'featureCodeCount' => count($featureCodes),
                'featureCodes' => array_map(function($fc) { return $fc['code'] ?? 'NO_CODE'; }, $featureCodes),
            ]);

            foreach ($featureCodes as $featureCode) {
                if (isset($featureCode['code']) && in_array($featureCode['code'], ['MOP_ID', 'ORDER_FORM_ID'])) {
                    // If order information is stored in feature codes, extract it
                    // This is a potential future enhancement
                    $this->logger->info(__METHOD__ . ' Found order-related feature code', [
                        'vin' => $vehicle->getVin(),
                        'userId' => $userId,
                        'featureCode' => $featureCode,
                    ]);
                }
            }

            // Method 2: Check raw MongoDB document for legacy vehicleOrder data
            // Enhanced to handle null VINs with fallback logic
            $vin = $vehicle->getVin();

            $this->logger->info(__METHOD__ . ' Attempting to get raw vehicle order data', [
                'vin' => $vin,
                'userId' => $userId,
                'vinIsNull' => $vin === null,
            ]);

            // Try to get order data from raw MongoDB (handles null VINs with fallback)
            $rawOrderData = $this->userDataService->getRawVehicleOrderData($userId, $vin);

            $this->logger->info(__METHOD__ . ' Raw vehicle order data result', [
                'vin' => $vin,
                'userId' => $userId,
                'rawOrderDataExists' => $rawOrderData !== null,
                'rawOrderData' => $rawOrderData,
            ]);

            if ($rawOrderData) {
                $this->logger->info(__METHOD__ . ' Successfully found order data in raw MongoDB', [
                    'vin' => $vin,
                    'userId' => $userId,
                    'mopId' => $rawOrderData['mopId'] ?? null,
                    'orderFormId' => $rawOrderData['orderFormId'] ?? null,
                    'trackingStatus' => $rawOrderData['trackingStatus'] ?? null,
                    'orderFormStatus' => $rawOrderData['orderFormStatus'] ?? null,
                    'extractionMethod' => $vin ? 'VIN_match' : 'fallback_search',
                ]);
                return $rawOrderData;
            } else {
                $this->logger->warning(__METHOD__ . ' No order data found in raw MongoDB', [
                    'userId' => $userId,
                    'vin' => $vin,
                    'vehicleId' => $vehicle->getDocumentId() ?? 'unknown',
                ]);
            }

            // Method 3: Check if the vehicle is marked as an order (isOrder = true)
            // and try to derive order information from other sources
            $isOnOrder = $this->isVehicleOnOrder($vehicle);
            $this->logger->info(__METHOD__ . ' Checking if vehicle is on order', [
                'vin' => $vehicle->getVin(),
                'userId' => $userId,
                'isOnOrder' => $isOnOrder,
            ]);

            if ($isOnOrder) {
                // If vehicle is on order but no order data found, it might be a data migration issue
                $this->logger->warning(__METHOD__ . ' Vehicle marked as order but no order data found', [
                    'vin' => $vehicle->getVin(),
                    'userId' => $userId,
                ]);
            }

            $this->logger->info(__METHOD__ . ' No order information found for vehicle', [
                'vin' => $vehicle->getVin(),
                'userId' => $userId,
            ]);

            return null;
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Error extracting order information', [
                'vin' => $vehicle->getVin(),
                'userId' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return null;
        }
    }
}
