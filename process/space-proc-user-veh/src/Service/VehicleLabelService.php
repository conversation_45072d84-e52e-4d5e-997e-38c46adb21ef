<?php

namespace App\Service;

use App\Helper\LcdvsProvider;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Service\MongoDBService;
use Space\MongoDocuments\Document\VehicleLabel;
use Space\MongoDocuments\Repository\VehicleLabelRepository;
use Symfony\Component\Serializer\SerializerInterface;

class VehicleLabelService
{
    use LoggerTrait;

    public const COLLECTION = 'vehicleLabel';

    public function __construct(
        private MongoDBService $mongoDBService,
        private SerializerInterface $serializer
    )
    {
    }

    public function getVehicleLabelDocumentByLcdv(string $lcdvCode): WSResponse
    {
        try {
            /** @var VehicleLabelRepository $repository */
            $repository = $this->mongoDBService->getRepository(VehicleLabel::class);
            $vehicleLabel = $repository->findByLcdvWithLongestMatch($lcdvCode);

            if ($vehicleLabel) {
                $documents = [
                    [
                        '_id' => $vehicleLabel->getId(),
                        'lcdv' => $vehicleLabel->getLcdv(),
                        'label' => $vehicleLabel->getLabel(),
                        'isO2X' => $vehicleLabel->getIsO2X(),
                        'sdp' => $vehicleLabel->getSdp()
                    ]
                ];

                $responseData = json_encode(['documents' => $documents]);
                return new WSResponse(200, $responseData);
            } else {
                $this->logger->warning(__METHOD__ . ' No vehicle label found for LCDV', [
                    'lcdvCode' => $lcdvCode
                ]);
                return new WSResponse(404, json_encode(['documents' => []]));
            }
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Error retrieving vehicle label', [
                'lcdvCode' => $lcdvCode,
                'error' => $e->getMessage()
            ]);
            return new WSResponse(500, json_encode(['documents' => []]));
        }
    }

    public function findLabelByLongestMatchingLcdv(string $lcdvCode): ?string
    {
        try {
            /** @var VehicleLabelRepository $repository */
            $repository = $this->mongoDBService->getRepository(VehicleLabel::class);
            $vehicleLabel = $repository->findByLcdvWithLongestMatch($lcdvCode);

            return $vehicleLabel?->getLabel();
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Error finding label by LCDV', [
                'lcdvCode' => $lcdvCode,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
}