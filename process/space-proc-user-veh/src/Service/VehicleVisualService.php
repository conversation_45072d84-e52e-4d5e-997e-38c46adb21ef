<?php

namespace App\Service;

use App\Helper\LcdvsProvider;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Service\MongoDBService;
use Space\MongoDocuments\Document\VehicleLabel;
use Space\MongoDocuments\Repository\VehicleLabelRepository;

class VehicleVisualService
{
    use LoggerTrait;

    /**
     */
    public function __construct(
        private MongoDBService $mongoDBService,
        private array $visualSettings
    ) {
    }

    /**
     * get visual vehicle from BO (v3D or default image..)
     */
    public function getVisualVehicle(?string $lcdv, string $brand): array
    {
        $this->logger->info('Get LVH Label');
        $projection = [
            'visualSettings' => 1
        ];

        try {
            /** @var VehicleLabelRepository $repository */
            $repository = $this->mongoDBService->getRepository(VehicleLabel::class);
            $vehicleLabel = $repository->findByLcdvWithLongestMatch($lcdv);

            if (!$vehicleLabel) {
                $this->logger->warning(__METHOD__ . ' No vehicle label found for LCDV', [
                    'lcdv' => $lcdv,
                    'brand' => $brand
                ]);
                return [];
            }

            // Convert VehicleLabel to array format for compatibility
            $response = [
                '_id' => $vehicleLabel->getId(),
                'lcdv' => $vehicleLabel->getLcdv(),
                'label' => $vehicleLabel->getLabel(),
                'isO2X' => $vehicleLabel->getIsO2X(),
                'sdp' => $vehicleLabel->getSdp()
            ];

        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Error retrieving vehicle label', [
                'lcdv' => $lcdv,
                'brand' => $brand,
                'error' => $e->getMessage()
            ]);
            return [];
        }
        if(isset($response['visualSettings']))
        {

            $response['visualSettings']['visual'] = $this->visualSettings[$brand]['baseUrl'];
            
            if(!isset($response['visualSettings']['width']) || empty($response['visualSettings']['width'])){
                $response['visualSettings']['width'] = $this->visualSettings['width'];
            }
            
            if(!isset($response['visualSettings']['height']) || empty($response['visualSettings']['height'])){
                $response['visualSettings']['height'] = $this->visualSettings['height'];
            }
        }

        return $response;
    }
}
