<?php

namespace App\Service;

use App\Trait\LoggerTrait;
use App\Service\FeatureCodeService;
use App\Helper\VehicleTypeEntities;
use Space\MongoDocuments\Document\Vehicle;
use App\Service\UserDataService;
use App\Service\VehicleCorvetService;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\FeatureCode;
use Symfony\Component\HttpFoundation\Response;

/**
 * Service for handling vehicle feature code operations
 */
class VehicleFeatureService
{
    use LoggerTrait;

    public function __construct(
        private FeatureCodeService $featureCodeService,
        private UserDataService $userDataService,
        private VehicleCorvetService $vehicleCorvetService
    ) {
    }

    /**
     * Get vehicle features for a specific vehicle (API endpoint method)
     */
    public function getVehicleFeatures(string $userId, string $vin)
    {
        try {
            // Verify the vehicle belongs to the user
            $vehicleResponse = $this->userDataService->getVehicle($userId, $vin);

            if (!$vehicleResponse) {
                $this->logger->error(__METHOD__ . ' : Error vehicle not exist');
                return new ErrorResponse('Vehicle does not exist', Response::HTTP_NOT_FOUND);
            }

            $vehicleObject = $vehicleResponse['vehicle'][0] ?? null;
            $userDbId = $vehicleResponse['userDbId'] ?? null;

            if (!$vehicleObject) {
                return new ErrorResponse('Vehicle data not found', Response::HTTP_NOT_FOUND);
            }

            // Convert ODM Vehicle object to array for backward compatibility
            $vehicle = [
                'vin' => $vehicleObject->getVin(),
                'brand' => $vehicleObject->getBrand(),
                'model' => $vehicleObject->getModel(),
                'versionId' => $vehicleObject->getVersionId(),
                'status' => $vehicleObject->getStatus(),
                'featureCode' => $vehicleObject->getFeatureCode() ?? [],
                'featureCodeExpiry' => null, // ODM Vehicle doesn't have this field
                'type' => $vehicleObject->getType(), // Get type from ODM Vehicle object
            ];

            // Extract feature codes from the vehicle data
            $featureCodes = $vehicle['featureCode'] ?? [];
            $featureCodeExpiry = $vehicle['featureCodeExpiry'] ?? null;

            $currentTime = time();

            // Check if feature codes have expired
            if ($featureCodeExpiry === null || $currentTime > $featureCodeExpiry) {
                $this->logger->info(__METHOD__ . ' : Feature codes have expired or no expiry set, regenerating...', [
                    'userId' => $userId,
                    'vin' => $vin,
                    'currentTime' => $currentTime,
                    'featureCodeExpiry' => $featureCodeExpiry
                ]);

                $brand = $vehicle['brand'] ?? null;
                $vehicleType = $vehicle['type'] ?? null;

                // Validate brand before calling Corvet API
                if (empty($brand)) {
                    $this->logger->error(__METHOD__ . ' : Error vehicle brand is empty for feature code generation', [
                        'vin' => $vin,
                        'userId' => $userId,
                        'vehicleData' => $vehicle
                    ]);
                    return new ErrorResponse('Vehicle brand is required for feature code generation', Response::HTTP_BAD_REQUEST);
                }

                // If vehicleType is null, we'll derive it from Corvet data later
                if (empty($vehicleType)) {
                    $this->logger->info(__METHOD__ . ' : Vehicle type is null, will derive from Corvet data', [
                        'vin' => $vin,
                        'userId' => $userId,
                        'brand' => $brand
                    ]);
                }

                //Call corvet API
                $corvetData = $this->vehicleCorvetService->getCorvetData($vin, $brand);
                // $corvetData = $this->vehicleCorvetService->mockCorvetData(); //TODO remove this
                $lcdv = $this->vehicleCorvetService->getLcdv($corvetData);
                if (!$lcdv) {
                    $this->logger->error(__METHOD__ . ' : Error lcdv not found while calling corvet');
                    return new ErrorResponse('Error lcdv not found while calling corvet, while getting updated feature codes', Response::HTTP_INTERNAL_SERVER_ERROR);
                }
                $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
                $corvertAttributs = $this->getManagedAttributes($allAttributes);

                // If vehicleType is null, derive it from Corvet data
                if (empty($vehicleType)) {
                    $vehicleTypeNumber = VehicleTypeEntities::getType($brand, $corvertAttributs);
                    $vehicleType = VehicleTypeEntities::TYPES[$vehicleTypeNumber] ?? '';
                    $this->logger->info(__METHOD__ . ' : Derived vehicle type from Corvet data', [
                        'vin' => $vin,
                        'userId' => $userId,
                        'vehicleTypeNumber' => $vehicleTypeNumber,
                        'vehicleType' => $vehicleType
                    ]);
                }

                // Ensure vehicleType is not null for the service call
                if (empty($vehicleType)) {
                    $this->logger->warning(__METHOD__ . ' : Vehicle type is still empty after derivation, using default', [
                        'vin' => $vin,
                        'userId' => $userId
                    ]);
                    $vehicleType = 'UNKNOWN'; // Default fallback
                }

                // Regenerate feature codes
                $featureCodes = $this->featureCodeService->getFeaturesCode($userId, $vin, $lcdv, $vehicleType, $corvertAttributs, null, null, false, $userDbId);

                // get existing NON FDS feature codes
                $existingNonFDSFeatures = [];
                $currentFeatureCodes = $vehicle['featureCode'] ?? [];
                $nonFdsKeys = FeatureCode::getNonFdsFeatureKeys();
                foreach ($currentFeatureCodes as $featureCode) {
                    if (in_array($featureCode['code'], $nonFdsKeys)) {
                        $existingNonFDSFeatures[] = $featureCode;
                    }
                }
                $featureCodes = array_merge($featureCodes, $existingNonFDSFeatures);

                // Update the vehicle document with new feature codes and expiry
                $this->userDataService->updateFeatureCodes($userId, $vin, $featureCodes);
            }

            if (empty($featureCodes)) {
                $this->logger->info(__METHOD__ . ' : No feature codes found for vehicle', [
                    'userId' => $userId,
                    'vin' => $vin
                ]);
                // Return empty array instead of error to maintain consistent response structure
                return new SuccessResponse(['features' => []]);
            }

            return new SuccessResponse(['features' => $featureCodes]);
        } catch (\Exception $e) {
            $this->logger->error('Error getting  vehicle features: ' . $e->getMessage());
            return new ErrorResponse('Failed to retrieve vehicle features', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get vehicle features for a specific vehicle (internal method)
     */
    public function getVehicleFeaturesInternal(string $userId, string $vin, Vehicle $vehicle): array
    {
        try {
            $this->logger->info(__METHOD__ . ' Getting vehicle features', [
                'userId' => $userId,
                'vin' => $vin
            ]);

            // Get existing feature codes from vehicle
            $existingFeatureCodes = $vehicle->getFeatureCode() ?? [];
            
            // Get additional feature codes based on vehicle type and country
            $vehicleType = $vehicle->getType();
            $country = $vehicle->getCountry();
            
            $additionalFeatures = [];
            
            // Add CSM feature code if applicable
            $csmFeature = $this->featureCodeService->getChargingStationManagementFeature(
                $vehicleType, 
                null, 
                $country
            );
            
            if ($csmFeature !== null) {
                $additionalFeatures[] = $csmFeature;
            }

            // Merge existing and additional features
            $allFeatures = array_merge($existingFeatureCodes, $additionalFeatures);
            
            // Remove duplicates based on feature code
            $uniqueFeatures = [];
            $seenCodes = [];
            
            foreach ($allFeatures as $feature) {
                $code = $feature['code'] ?? $feature;
                if (!in_array($code, $seenCodes)) {
                    $uniqueFeatures[] = $feature;
                    $seenCodes[] = $code;
                }
            }

            $this->logger->info(__METHOD__ . ' Vehicle features retrieved successfully', [
                'userId' => $userId,
                'vin' => $vin,
                'featureCount' => count($uniqueFeatures)
            ]);

            return $uniqueFeatures;

        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Error getting vehicle features', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Update feature codes for a vehicle
     */
    public function updateVehicleFeatureCodes(
        Vehicle $vehicle,
        string $userId,
        string $vin,
        ?string $country = null
    ): array {
        try {
            $this->logger->info(__METHOD__ . ' Updating vehicle feature codes', [
                'userId' => $userId,
                'vin' => $vin
            ]);

            $existingFeatureCodes = $vehicle->getFeatureCode() ?? [];
            $vehicleType = $vehicle->getType();
            $vehicleCountry = $country ?? $vehicle->getCountry();

            // Get updated feature codes from service
            $updatedFeatures = $this->featureCodeService->getChargingStationManagementFeature(
                $vehicleType,
                null,
                $vehicleCountry
            );

            if ($updatedFeatures !== null) {
                // Check if feature already exists
                $featureExists = false;
                foreach ($existingFeatureCodes as $existingFeature) {
                    if (($existingFeature['code'] ?? $existingFeature) === ($updatedFeatures['code'] ?? $updatedFeatures)) {
                        $featureExists = true;
                        break;
                    }
                }

                // Add feature if it doesn't exist
                if (!$featureExists) {
                    $existingFeatureCodes[] = $updatedFeatures;
                }
            }

            $this->logger->info(__METHOD__ . ' Vehicle feature codes updated successfully', [
                'userId' => $userId,
                'vin' => $vin,
                'featureCount' => count($existingFeatureCodes)
            ]);

            return $existingFeatureCodes;

        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Error updating vehicle feature codes', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            return $vehicle->getFeatureCode() ?? [];
        }
    }

    /**
     * Get managed attributes from Corvet data
     */
    public function getManagedAttributes(array $attributes): ?array
    {
        $managedAttributes = [];
        foreach ($attributes as $attribute) {
            if (isset($attribute['ATTRIBUT_CODE'])) {
                $managedAttributes[$attribute['ATTRIBUT_CODE']] = $attribute['ATTRIBUT_VALUE'];
            }
        }
        return $managedAttributes;
    }

    /**
     * Get vehicle type number from attributes
     */
    public function getVehicleTypeFromAttributes(string $brand, array $attributes): int
    {
        return VehicleTypeEntities::getType($brand, $attributes);
    }

    /**
     * Test feature codes (for testing purposes only)
     */
    public function testFeatureCodes(array $corvetData, string $lcdv): array
    {
        try {
            $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
            $corvetAttributes = $this->getManagedAttributes($allAttributes);
            $vehicleTypeNumber = VehicleTypeEntities::getType('DXD', $corvetAttributes);
            $vehicleType = VehicleTypeEntities::TYPES[$vehicleTypeNumber] ?? '';

            $this->logger->info(__METHOD__ . ' Testing feature codes', [
                'lcdv' => $lcdv,
                'vehicleType' => $vehicleType,
                'attributeCount' => count($corvetAttributes)
            ]);

            // Get feature codes for testing
            $featureCodes = $this->featureCodeService->getFeaturesCode($lcdv, $corvetAttributes);

            return [
                'vehicleType' => $vehicleType,
                'vehicleTypeNumber' => $vehicleTypeNumber,
                'attributes' => $corvetAttributes,
                'featureCodes' => $featureCodes
            ];

        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Error testing feature codes', [
                'lcdv' => $lcdv,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Set eligibility from contracts/subscriptions
     */
    public function setEligibilityFromContracts(?array $subscriptions): array
    {
        $eligibilities = [];
        if (!$subscriptions) {
            return $eligibilities;
        }

        foreach ($subscriptions as $subscription) {
            if (isset($subscription['productCode'])) {
                switch ($subscription['productCode']) {
                    case 'NAVCOZAR':
                        $eligibilities[] = 'navcozar';
                        break;
                    case 'REMOTELEV':
                        $eligibilities[] = 'remotelev';
                        break;
                    case 'NAC':
                        $eligibilities[] = 'nac';
                        break;
                }
            }
        }

        return array_unique($eligibilities);
    }

    /**
     * Get vehicle type number from string
     */
    public function getVehicleType(string $vehicleType): int
    {
        switch (strtoupper($vehicleType)) {
            case "ICE":
                return 0;
            case "HEV":
                return 2;
            case "PHEV":
                return 3;
            case "BEV":
                return 4;
            case "MHEV":
                return 5;
            case "HFCV":
                return 6;
            default:
                return 10;
        }
    }

    /**
     * Get managed attributes from Corvet attributes array
     * Filters attributes based on specific prefixes
     */
    public function getManagedAttributesByPrefix(array $attributes): ?array
    {
        $managedAttributes = [];
        foreach ($attributes as $attribute) {
            switch (substr($attribute, 0, 3)) {
                case 'DCX':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DXD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DCD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRE':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRC':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DMW':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DVQ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DJY':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'D7K':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DME':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DE2':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DZZ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DLX':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DO9':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'D32':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DYR':
                    if (substr($attribute, 3, 2) == "17") {
                        $managedAttributes[] = trim($attribute);
                    }
                    break;
            }
        }
        return $managedAttributes;
    }
}
