<?php

namespace App\Service;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Document\Vehicle;
use Space\MongoDocuments\Service\MongoDBService;
use Symfony\Component\HttpFoundation\Response;
use App\Model\SystemVehicleData;
use App\Model\VehicleModel;

/**
 * VehicleDataService handles vehicle-specific ODM operations
 * Following the architectural pattern from other microservices where Manager classes
 * don't directly call MongoDB ODM but use dedicated service layers
 */
class VehicleDataService
{
    use LoggerTrait;

    public function __construct(
        private MongoDBService $mongoDBService,
        private UserDataService $userDataService,
        private VehicleMapperService $vehicleMapperService,
        private VehicleTransformationService $vehicleTransformationService,
        private FeatureCodeService $featureCodeService,
        private VehicleUtilityService $vehicleUtilityService
    ) {
    }

    /**
     * Find vehicle by user ID and VIN
     */
    public function findVehicleByUserAndVin(string $userId, string $vin): ?Vehicle
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Finding vehicle by user and VIN', [
                'userId' => $userId,
                'vin' => $vin,
            ]);

            return $this->userDataService->findUserVehicleByVin($userId, $vin);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error finding vehicle', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get all vehicles for a user
     */
    public function getUserVehicles(string $userId): array
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting user vehicles', [
                'userId' => $userId,
            ]);

            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                return [];
            }

            return $userData->getVehicles()->toArray();
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting user vehicles', [
                'userId' => $userId,
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * Add vehicle to user document
     */
    public function addVehicleToUser(string $userId, Vehicle $vehicle): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Adding vehicle to user', [
                'userId' => $userId,
                'vin' => $vehicle->getVin(),
            ]);

            $result = $this->userDataService->addVehicleToUserDocument($userId, $vehicle);

            if ($result) {
                return new SuccessResponse(['message' => 'Vehicle added successfully'], Response::HTTP_OK);
            } else {
                return new ErrorResponse('Failed to add vehicle to user', Response::HTTP_INTERNAL_SERVER_ERROR);
            }
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error adding vehicle to user', [
                'userId' => $userId,
                'vin' => $vehicle->getVin(),
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse('Error adding vehicle to user', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update vehicle in user document
     */
    public function updateVehicleInUser(string $userId, Vehicle $vehicle): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updating vehicle in user document', [
                'userId' => $userId,
                'vin' => $vehicle->getVin(),
            ]);

            return $this->userDataService->updateVehicleInUserDocument($userId, $vehicle);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error updating vehicle in user document', [
                'userId' => $userId,
                'vin' => $vehicle->getVin(),
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse('Error updating vehicle', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove vehicle from user document
     */
    public function removeVehicleFromUser(string $userId, string $vin): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Removing vehicle from user', [
                'userId' => $userId,
                'vin' => $vin,
            ]);

            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                return new ErrorResponse('User not found', Response::HTTP_NOT_FOUND);
            }

            // Find and remove the vehicle
            $vehicles = $userData->getVehicles();
            $vehicleToRemove = null;
            
            foreach ($vehicles as $vehicle) {
                if ($vehicle->getVin() === $vin) {
                    $vehicleToRemove = $vehicle;
                    break;
                }
            }

            if (!$vehicleToRemove) {
                return new ErrorResponse('Vehicle not found', Response::HTTP_NOT_FOUND);
            }

            $vehicles->removeElement($vehicleToRemove);
            
            return $this->userDataService->saveUserData($userData);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error removing vehicle from user', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse('Error removing vehicle', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get vehicles by brand for a user
     */
    public function getUserVehiclesByBrand(string $userId, string $brand): array
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting user vehicles by brand', [
                'userId' => $userId,
                'brand' => $brand,
            ]);

            return $this->userDataService->getVehicleByUserIdAndBrand($userId, $brand);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting user vehicles by brand', [
                'userId' => $userId,
                'brand' => $brand,
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * Check if vehicle exists for user
     */
    public function vehicleExistsForUser(string $userId, string $vin): bool
    {
        try {
            $vehicle = $this->findVehicleByUserAndVin($userId, $vin);
            return $vehicle !== null;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error checking vehicle existence', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get vehicle count for user
     */
    public function getUserVehicleCount(string $userId): int
    {
        try {
            $vehicles = $this->getUserVehicles($userId);
            return count($vehicles);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting vehicle count', [
                'userId' => $userId,
                'error' => $e->getMessage(),
            ]);
            return 0;
        }
    }

    /**
     * Get PSA ID for user and brand
     */
    public function getPsaIdByUserAndBrand(string $userId, string $brand): ?string
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting PSA ID', [
                'userId' => $userId,
                'brand' => $brand,
            ]);

            return $this->userDataService->getPsaIdByUserAndBrand($userId, $brand);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting PSA ID', [
                'userId' => $userId,
                'brand' => $brand,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Remove SSDP vehicles for user
     */
    public function removeSSDPVehicles(string $userId): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Removing SSDP vehicles', [
                'userId' => $userId,
            ]);

            return $this->userDataService->removeUserSSDPVehicles($userId);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error removing SSDP vehicles', [
                'userId' => $userId,
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse('Error removing SSDP vehicles', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Insert or update vehicle data in user document
     */
    public function saveUserDataDocumentVehicles(string $userId, string $vin, SystemVehicleData $discoveredVehicle, ?string $country = null): void
    {
        $userVehicle = $this->findVehicleByUserAndVin($userId, $vin);
        if (null === $userVehicle) {
            // add the vehicle if it doesn't exist using VehicleMapperService
            $userVehicle = $this->vehicleMapperService->createVehicleFromSystemData($discoveredVehicle);
            $userVehicle->setDocumentId($this->vehicleTransformationService->generateVehicleId());

            // Adding CSM feature code here
            $featureCode = $this->featureCodeService->getChargingStationManagementFeature($userVehicle->getType(), null, $country);
            if ($featureCode !== null) {
                $existingFeatureCodes = $userVehicle->getFeatureCode() ?? [];
                $existingFeatureCodes[] = $featureCode;
                $userVehicle->setFeatureCode($existingFeatureCodes);
            }

            $this->addVehicleToUser($userId, $userVehicle);
        } else {
            // update the vehicle if it exists using ODM methods
            $this->updateVehicleFromSystemData($userVehicle, $discoveredVehicle);
            $this->updateVehicleInUser($userId, $userVehicle);
        }
    }

    /**
     * Update vehicle properties from system data
     */
    private function updateVehicleFromSystemData(Vehicle $userVehicle, SystemVehicleData $systemVehicle): void
    {
        // Update vehicle properties from system data
        if ($systemVehicle->getLabel()) {
            $userVehicle->setLabel($systemVehicle->getLabel());
        }
        if ($systemVehicle->getBrand()) {
            $userVehicle->setBrand($systemVehicle->getBrand());
        }
        if ($systemVehicle->getModel()) {
            $userVehicle->setModel($systemVehicle->getModel());
        }
        if ($systemVehicle->getVersionId()) {
            $userVehicle->setVersionId($systemVehicle->getVersionId());
        }
        if ($systemVehicle->getType()) {
            $userVehicle->setType($systemVehicle->getType());
        }
        if ($systemVehicle->getRegTimeStamp()) {
            $userVehicle->setRegTimeStamp($systemVehicle->getRegTimeStamp());
        }
        if ($systemVehicle->getEnrollmentStatus()) {
            $userVehicle->setEnrollmentStatus($systemVehicle->getEnrollmentStatus());
        }
        if ($systemVehicle->getConnectorType()) {
            $userVehicle->setConnectorType($systemVehicle->getConnectorType());
        }
        if ($systemVehicle->getMake()) {
            $userVehicle->setMake($systemVehicle->getMake());
        }
        if ($systemVehicle->getSubMake()) {
            $userVehicle->setSubMake($systemVehicle->getSubMake());
        }
        if ($systemVehicle->getMarket()) {
            $userVehicle->setMarket($systemVehicle->getMarket());
        }
        if ($systemVehicle->getLastUpdate()) {
            $userVehicle->setLastUpdate($systemVehicle->getLastUpdate());
        }
        if ($systemVehicle->getYear()) {
            $userVehicle->setYear($systemVehicle->getYear());
        }
        if ($systemVehicle->getWarrantyStartDate()) {
            $userVehicle->setWarrantyStartDate($systemVehicle->getWarrantyStartDate());
        }
    }

    /**
     * ODM-based vehicle creation/update to replace legacy VehicleService
     */
    public function createOrUpdateVehicleODM(string $userId, VehicleModel $vehicleModel): bool
    {
        try {
            // Convert VehicleModel to ODM Vehicle format
            $vehicleData = [
                'vin' => $vehicleModel->getVin(),
                'brand' => $vehicleModel->getBrand(),
                'model' => $vehicleModel->getModel(),
                'versionId' => $vehicleModel->getVersionId(),
                'label' => $vehicleModel->getLabel(),
                'visual' => $vehicleModel->getVisual(),
                'country' => $vehicleModel->getCountry(),
                'language' => $vehicleModel->getLanguage(),
                'isOrder' => $vehicleModel->isIsOrder(),
                'addStatus' => $vehicleModel->getAddStatus(),
            ];

            // Handle vehicle order data
            if ($vehicleModel->getVehicleOrder()) {
                $vehicleData['vehicleOrder'] = [
                    'mopId' => $vehicleModel->getVehicleOrder()->getMopId(),
                    'orderFormId' => $vehicleModel->getVehicleOrder()->getOrderFormId(),
                    'trackingStatus' => $vehicleModel->getVehicleOrder()->getTrackingStatus(),
                    'orderFormStatus' => $vehicleModel->getVehicleOrder()->getOrderFormStatus(),
                    'isUpdated' => true, // Mark as updated
                ];
            }

            // Check if vehicle already exists
            $existingVehicle = $this->findVehicleByUserAndVin($userId, $vehicleModel->getVin());

            if ($existingVehicle) {
                // Update existing vehicle
                // Update the existing vehicle with new data
                foreach ($vehicleData as $key => $value) {
                    if ($key === 'vehicleOrder' && is_array($value)) {
                        // Handle vehicle order updates
                        continue; // Skip for now, would need specific order update logic
                    }
                    // Update other fields as needed
                }

                return $this->updateVehicleInUser($userId, $existingVehicle);
            } else {
                // Create new vehicle
                // Convert to legacy format for compatibility
                $legacyVehicle = (object) $vehicleData;
                $legacyVehicle->id = $this->vehicleUtilityService->generateNewUuid();

                return $this->addVehicleToUser($userId, $legacyVehicle);
            }
        } catch (\Exception $e) {
            return false;
        }
    }
}
