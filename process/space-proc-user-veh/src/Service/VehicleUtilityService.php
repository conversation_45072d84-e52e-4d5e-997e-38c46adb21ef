<?php

namespace App\Service;

use App\Trait\LoggerTrait;
use App\Helper\CultureHelper;
use App\Model\VehicleModel;
use App\Dto\AddVehicleInputDTO;
use App\Dto\AddVehicleOutputDTO;
use App\Connector\WSResponse as ConnectorWSResponse;
use Space\MongoDocuments\Document\Vehicle;
use Symfony\Component\Uid\Uuid;

/**
 * Service for utility and helper methods related to vehicles
 */
class VehicleUtilityService
{
    use LoggerTrait;

    /**
     * Remove null values from array recursively
     */
    public function removeNullValues(array $array): array
    {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = $this->removeNullValues($value);
            } elseif (is_null($value)) {
                unset($array[$key]);
            }
        }
        return $array;
    }

    /**
     * Generate new UUID
     */
    public function generateNewUuid(): string
    {
        return (string) Uuid::v4();
    }

    /**
     * Get vehicle model from content array
     */
    public function getVehicleModel(array $content): VehicleModel
    {
        $culture = CultureHelper::parseCulture($content['culture']);
        $content['country'] = $culture['country'];
        $content['language'] = $culture['language'];

        $vehicleModel = new VehicleModel();
        $vehicleModel->setUserId($content['userId']);
        $vehicleModel->setVin($content['vin']);
        $vehicleModel->setLabel($content['label']);
        $vehicleModel->setCountry($content['country']);
        $vehicleModel->setLanguage($content['language']);
        $vehicleModel->setBrand($content['brand']);
        
        return $vehicleModel;
    }

    /**
     * Filter vehicles by criteria
     */
    public function filterByCriteria(array $vehicles, string $criteriaValue, string $criteriaKey = 'vin'): ?array
    {
        $data = current(array_filter($vehicles, function ($vehicle) use ($criteriaValue, $criteriaKey) {
            return $vehicle[$criteriaKey] == $criteriaValue;
        }));
        return $data ?: null;
    }

    /**
     * Extract user DB ID from ODM user data
     */
    public function extractUserDbIdFromODMUserData($userData): ?string
    {
        if (!$userData) {
            return null;
        }

        // Handle both ODM UserData object and array format
        if (is_object($userData) && method_exists($userData, 'getUserDbId')) {
            return $userData->getUserDbId();
        }

        if (is_array($userData) && isset($userData['userDbId'])) {
            return $userData['userDbId'];
        }

        // Fallback to userId if userDbId is not available
        if (is_object($userData) && method_exists($userData, 'getUserId')) {
            return $userData->getUserId();
        }

        if (is_array($userData) && isset($userData['userId'])) {
            return $userData['userId'];
        }

        return null;
    }

    /**
     * Convert ODM Vehicle to legacy format for backward compatibility
     */
    public function convertODMVehicleToLegacy(Vehicle $odmVehicle): \App\MongoDB\UserData\UserDataDocument\Vehicle
    {
        $legacyVehicle = new \App\MongoDB\UserData\UserDataDocument\Vehicle();

        $legacyVehicle->id = $odmVehicle->getDocumentId() ?? $this->generateNewUuid();
        $legacyVehicle->vin = $odmVehicle->getVin();
        $legacyVehicle->label = $odmVehicle->getLabel();
        $legacyVehicle->brand = $odmVehicle->getBrand();
        $legacyVehicle->model = $odmVehicle->getModel();
        $legacyVehicle->versionId = $odmVehicle->getVersionId();
        $legacyVehicle->type = $odmVehicle->getType();
        $legacyVehicle->country = $odmVehicle->getCountry();
        $legacyVehicle->language = $odmVehicle->getLanguage();
        $legacyVehicle->isOrder = $odmVehicle->getIsOrder() ?? false;
        $legacyVehicle->featureCode = $odmVehicle->getFeatureCode() ?? [];

        return $legacyVehicle;
    }

    /**
     * Get SDP value based on vehicle label SDP
     */
    public function getSdp(?string $vehicleLabelSdp = null): string
    {
        $sdp = 'CVMP'; // Default SDP
        if ($vehicleLabelSdp == 'SSDP') {
            $sdp = 'SSDP';
        }
        return $sdp;
    }

    /**
     * Map vehicle info to XP format
     */
    public function mapVehicleInfoXPFormat(
        array $vehicle, 
        array $catalogResponse, 
        array $subscriptionResponse, 
        array $productsStatus, 
        array $featureCodes
    ): array {
        $type = $this->getVehicleTypeNumber($vehicle['type'] ?? '');
        
        $vehicleXPFormat = [
            'id' => $vehicle['id'] ?? '',
            'vin' => $vehicle['vin'] ?? '',
            'label' => $vehicle['label'] ?? '',
            'versionId' => $vehicle['versionId'] ?? '',
            'brand' => $vehicle['brand'] ?? '',
            'visual' => $vehicle['visual'] ?? [],
            'language' => $vehicle['language'] ?? '',
            'country' => $vehicle['country'] ?? '',
            'isOrder' => $vehicle['isOrder'] ?? false,
            'type' => $type,
            'catalog' => $catalogResponse,
            'subscription' => $subscriptionResponse,
            'productsStatus' => $productsStatus,
            'featureCodes' => $featureCodes
        ];

        if (isset($vehicle['vehicleOrder'])) {
            $vehicleXPFormat['vehicleOrder'] = $vehicle['vehicleOrder'];
        }

        return $vehicleXPFormat;
    }

    /**
     * Get vehicle type number from string
     */
    private function getVehicleTypeNumber(string $vehicleType): int
    {
        switch (strtoupper($vehicleType)) {
            case "ICE":
                return 1;
            case "HEV":
                return 2;
            case "PHEV":
                return 3;
            case "BEV":
                return 4;
            default:
                return 0;
        }
    }

    /**
     * Create data array for adding vehicle in Customer@
     */
    public function createCustomerAtVehicleData(
        string $vin,
        string $label,
        string $lcdv
    ): array {
        return [
            'VEH_VIN' => $vin,
            'VEH_CLASS_LABEL' => $label,
            'VEH_LCDV' => $lcdv,
            'VEH_VIS' => substr($vin, 9),
        ];
    }

    /**
     * Validate array structure and required fields
     */
    public function validateRequiredFields(array $data, array $requiredFields): array
    {
        $errors = [];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $errors[] = "Required field '{$field}' is missing or empty";
            }
        }
        
        return $errors;
    }

    /**
     * Sanitize and normalize vehicle data
     */
    public function sanitizeVehicleData(array $vehicleData): array
    {
        // Remove any potentially harmful data
        $sanitized = [];
        
        $allowedFields = [
            'id', 'vin', 'label', 'brand', 'model', 'versionId', 'type',
            'country', 'language', 'isOrder', 'featureCode', 'visual',
            'vehicleOrder', 'mileageData', 'registrationNumber', 'color',
            'energy', 'status', 'nickName', 'picture'
        ];
        
        foreach ($allowedFields as $field) {
            if (isset($vehicleData[$field])) {
                $sanitized[$field] = $vehicleData[$field];
            }
        }
        
        return $sanitized;
    }

    /**
     * Format response data consistently
     */
    public function formatApiResponse(array $data, bool $success = true, ?string $message = null): array
    {
        $response = [
            'success' => $success,
            'data' => $data
        ];
        
        if ($message) {
            $response['message'] = $message;
        }
        
        return $response;
    }



}
