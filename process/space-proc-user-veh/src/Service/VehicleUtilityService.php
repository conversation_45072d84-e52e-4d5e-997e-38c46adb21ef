<?php

namespace App\Service;

use App\Trait\LoggerTrait;
use App\Helper\CultureHelper;
use App\Model\VehicleModel;
use App\Dto\AddVehicleInputDTO;
use App\Dto\AddVehicleOutputDTO;
use App\Connector\WSResponse as ConnectorWSResponse;
use Space\MongoDocuments\Document\Vehicle;
use Symfony\Component\Uid\Uuid;
use App\Service\RefreshVehicleInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\HttpFoundation\Response;

/**
 * Service for utility and helper methods related to vehicles
 */
class VehicleUtilityService
{
    use LoggerTrait;

    public function __construct(
        private UserDataService $userDataService,
        private DenormalizerInterface $denormalizer,
        private VehicleOrderService $vehicleOrderService
    ) {
    }

    /**
     * Remove null values from array recursively
     */
    public function removeNullValues(array $array): array
    {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = $this->removeNullValues($value);
            } elseif (is_null($value)) {
                unset($array[$key]);
            }
        }
        return $array;
    }

    /**
     * Generate new UUID
     */
    public function generateNewUuid(): string
    {
        return (string) Uuid::v4();
    }

    /**
     * Get vehicle model from content array
     */
    public function getVehicleModel(array $content): VehicleModel
    {
        $culture = CultureHelper::parseCulture($content['culture']);
        $content['country'] = $culture['country'];
        $content['language'] = $culture['language'];

        $vehicleModel = new VehicleModel();
        $vehicleModel->setUserId($content['userId']);
        $vehicleModel->setVin($content['vin']);
        $vehicleModel->setLabel($content['label']);
        $vehicleModel->setCountry($content['country']);
        $vehicleModel->setLanguage($content['language']);
        $vehicleModel->setBrand($content['brand']);
        
        return $vehicleModel;
    }

    /**
     * Filter vehicles by criteria
     */
    public function filterByCriteria(array $vehicles, string $criteriaValue, string $criteriaKey = 'vin'): ?array
    {
        $data = current(array_filter($vehicles, function ($vehicle) use ($criteriaValue, $criteriaKey) {
            return $vehicle[$criteriaKey] == $criteriaValue;
        }));
        return $data ?: null;
    }

    /**
     * Extract user DB ID from ODM user data
     */
    public function extractUserDbIdFromODMUserData($userData): ?string
    {
        if (!$userData) {
            return null;
        }

        // Handle both ODM UserData object and array format
        if (is_object($userData) && method_exists($userData, 'getUserDbId')) {
            return $userData->getUserDbId();
        }

        if (is_array($userData) && isset($userData['userDbId'])) {
            return $userData['userDbId'];
        }

        // Fallback to userId if userDbId is not available
        if (is_object($userData) && method_exists($userData, 'getUserId')) {
            return $userData->getUserId();
        }

        if (is_array($userData) && isset($userData['userId'])) {
            return $userData['userId'];
        }

        return null;
    }

    /**
     * Convert ODM Vehicle to array format for backward compatibility
     */
    public function convertODMVehicleToArray(Vehicle $odmVehicle): array
    {
        return [
            'id' => $odmVehicle->getDocumentId() ?? $this->generateNewUuid(),
            'vin' => $odmVehicle->getVin(),
            'label' => $odmVehicle->getLabel(),
            'brand' => $odmVehicle->getBrand(),
            'model' => $odmVehicle->getModel(),
            'versionId' => $odmVehicle->getVersionId(),
            'type' => $odmVehicle->getType(),
            'country' => 'AT', // Default country since ODM Vehicle doesn't store country
            'language' => 'en', // Default language since ODM Vehicle doesn't store language
            'isOrder' => false, // Default value since ODM Vehicle doesn't store isOrder
            'featureCode' => $odmVehicle->getFeatureCode() ?? [],
        ];
    }



    /**
     * Map vehicle info to XP format
     */
    public function mapVehicleInfoXPFormat(
        array $vehicle, 
        array $catalogResponse, 
        array $subscriptionResponse, 
        array $productsStatus, 
        array $featureCodes
    ): array {
        $type = $this->getVehicleTypeNumber($vehicle['type'] ?? '');
        
        $vehicleXPFormat = [
            'id' => $vehicle['id'] ?? '',
            'vin' => $vehicle['vin'] ?? '',
            'label' => $vehicle['label'] ?? '',
            'versionId' => $vehicle['versionId'] ?? '',
            'brand' => $vehicle['brand'] ?? '',
            'visual' => $vehicle['visual'] ?? [],
            'language' => $vehicle['language'] ?? '',
            'country' => $vehicle['country'] ?? '',
            'isOrder' => $vehicle['isOrder'] ?? false,
            'type' => $type,
            'catalog' => $catalogResponse,
            'subscription' => $subscriptionResponse,
            'productsStatus' => $productsStatus,
            'featureCodes' => $featureCodes
        ];

        if (isset($vehicle['vehicleOrder'])) {
            $vehicleXPFormat['vehicleOrder'] = $vehicle['vehicleOrder'];
        }

        return $vehicleXPFormat;
    }

    /**
     * Get vehicle type number from string
     */
    private function getVehicleTypeNumber(string $vehicleType): int
    {
        switch (strtoupper($vehicleType)) {
            case "ICE":
                return 1;
            case "HEV":
                return 2;
            case "PHEV":
                return 3;
            case "BEV":
                return 4;
            default:
                return 0;
        }
    }

    /**
     * Create data array for adding vehicle in Customer@
     */
    public function createCustomerAtVehicleData(
        string $vin,
        string $label,
        string $lcdv
    ): array {
        return [
            'VEH_VIN' => $vin,
            'VEH_CLASS_LABEL' => $label,
            'VEH_LCDV' => $lcdv,
            'VEH_VIS' => substr($vin, 9),
        ];
    }

    /**
     * Validate array structure and required fields
     */
    public function validateRequiredFields(array $data, array $requiredFields): array
    {
        $errors = [];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $errors[] = "Required field '{$field}' is missing or empty";
            }
        }
        
        return $errors;
    }

    /**
     * Sanitize and normalize vehicle data
     */
    public function sanitizeVehicleData(array $vehicleData): array
    {
        // Remove any potentially harmful data
        $sanitized = [];
        
        $allowedFields = [
            'id', 'vin', 'label', 'brand', 'model', 'versionId', 'type',
            'country', 'language', 'isOrder', 'featureCode', 'visual',
            'vehicleOrder', 'mileageData', 'registrationNumber', 'color',
            'energy', 'status', 'nickName', 'picture'
        ];
        
        foreach ($allowedFields as $field) {
            if (isset($vehicleData[$field])) {
                $sanitized[$field] = $vehicleData[$field];
            }
        }
        
        return $sanitized;
    }

    /**
     * Format response data consistently
     */
    public function formatApiResponse(array $data, bool $success = true, ?string $message = null): array
    {
        $response = [
            'success' => $success,
            'data' => $data
        ];
        
        if ($message) {
            $response['message'] = $message;
        }
        
        return $response;
    }

    /**
     * Get SDP value based on vehicle label SDP
     */
    public function getSdp(?string $vehicleLabelSdp = null)
    {
        $sdp = RefreshVehicleInterface::SDP_CVMP;
        if ($vehicleLabelSdp == RefreshVehicleInterface::SDP_SSDP) {
            $sdp = RefreshVehicleInterface::SDP_SSDP;
        }

        return $sdp;
    }

    /**
     * Find vehicle by criteria with enhanced lookup strategies
     */
    public function getVehicle(string $userId, string $critariaValue, string $critariaKey = 'vin'): ?array
    {
        $userData = $this->userDataService->findUserById($userId);
        if (!$userData) {
            return null;
        }

        $vehicles = $userData->getVehicles();
        $vehicle = null;

        // Enhanced vehicle lookup with multi-strategy approach
        foreach ($vehicles as $v) {
            // Check multiple possible identifiers for the vehicle
            // 1. Check VIN (primary identifier)
            // 2. Check document ID (MongoDB 'id' field) for 'id' criteria
            // 3. Check other standard criteria

            if ($critariaKey === 'id') {
                // For 'id' criteria, check both VIN and documentId
                if ($v->getVin() === $critariaValue ||
                    $v->getDocumentId() === $critariaValue) {
                    $vehicle = $v;
                    break;
                }
            } else {
                // For other criteria, use the original matching logic
                $value = match($critariaKey) {
                    'vin' => $v->getVin(),
                    'brand' => $v->getBrand(),
                    'model' => $v->getModel(),
                    'versionId' => $v->getVersionId(),
                    default => null
                };

                if ($value === $critariaValue) {
                    $vehicle = $v;
                    break;
                }
            }
        }

        // If no direct match found and criteria is 'id', try to find by order data (mopId, orderFormId)
        if (!$vehicle && $critariaKey === 'id') {
            // Get raw user data once to avoid multiple MongoDB calls
            $rawUserData = $this->userDataService->getRawUserData($userId);
            if ($rawUserData && isset($rawUserData['vehicle'])) {
                foreach ($rawUserData['vehicle'] as $index => $vehicleData) {
                    $vehicleOrder = $vehicleData['vehicleOrder'] ?? null;
                    if ($vehicleOrder &&
                        (($vehicleOrder['mopId'] ?? '') === $critariaValue ||
                         ($vehicleOrder['orderFormId'] ?? '') === $critariaValue)) {

                        // Find the corresponding ODM vehicle by index or VIN
                        $matchedVehicle = null;
                        $vehicleVin = $vehicleData['vin'] ?? null;

                        if ($vehicleVin) {
                            // Try to find by VIN first
                            foreach ($vehicles as $v) {
                                if ($v->getVin() === $vehicleVin) {
                                    $matchedVehicle = $v;
                                    break;
                                }
                            }
                        }

                        // If found by VIN, use it; otherwise try by index
                        if ($matchedVehicle) {
                            $vehicle = $matchedVehicle;
                        } elseif (isset($vehicles[$index])) {
                            $vehicle = $vehicles[$index];
                        }

                        if ($vehicle) {
                            break;
                        }
                    }
                }
            }
        }

        if (!$vehicle) {
            return null;
        }

        // Return ODM Vehicle object wrapped in expected format for getVehicleDetail method
        return ['vehicle' => [$vehicle]];
    }

    /**
     * Get vehicle info by ID and convert to VehicleModel
     */
    public function getVehicleInfo(string $vehicleId, string $userId): VehicleModel
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting vehicle info', [
                'vehicleId' => $vehicleId,
                'userId' => $userId,
            ]);

            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                throw new \Exception('User not found', Response::HTTP_NOT_FOUND);
            }

            $vehicles = $userData->getVehicles();
            $vehicle = null;

            foreach ($vehicles as $v) {
                // Check multiple possible identifiers for the vehicle
                // 1. Check VIN (primary identifier)
                // 2. Check document ID (MongoDB 'id' field)
                // 3. Check if vehicleId matches any order-related identifiers from raw data

                $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Checking vehicle match', [
                    'vehicleId' => $vehicleId,
                    'vehicleVin' => $v->getVin(),
                    'vehicleDocumentId' => $v->getDocumentId(),
                ]);

                if ($v->getVin() === $vehicleId ||
                    $v->getDocumentId() === $vehicleId) {
                    $vehicle = $v;
                    $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Found vehicle by direct match', [
                        'vehicleId' => $vehicleId,
                        'matchType' => $v->getVin() === $vehicleId ? 'VIN' : 'documentId',
                    ]);
                    break;
                }
            }

            // If no direct match found, try to find by order data (mopId, orderFormId)
            // Use a more efficient approach by getting raw data once and checking all vehicles
            if (!$vehicle) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' No direct match found, checking order data', [
                    'vehicleId' => $vehicleId,
                    'userId' => $userId,
                ]);

                // Get raw user data once to avoid multiple MongoDB calls
                $rawUserData = $this->userDataService->getRawUserData($userId);
                if ($rawUserData && isset($rawUserData['vehicle'])) {
                    foreach ($rawUserData['vehicle'] as $index => $vehicleData) {
                        $vehicleOrder = $vehicleData['vehicleOrder'] ?? null;
                        if ($vehicleOrder &&
                            (($vehicleOrder['mopId'] ?? '') === $vehicleId ||
                             ($vehicleOrder['orderFormId'] ?? '') === $vehicleId)) {

                            // Find the corresponding ODM vehicle by index or VIN
                            $matchedVehicle = null;
                            $vehicleVin = $vehicleData['vin'] ?? null;

                            if ($vehicleVin) {
                                // Try to find by VIN first
                                foreach ($vehicles as $v) {
                                    if ($v->getVin() === $vehicleVin) {
                                        $matchedVehicle = $v;
                                        break;
                                    }
                                }
                            }

                            // If still not found, try by index (less reliable but fallback)
                            if (!$matchedVehicle && $index < count($vehicles)) {
                                $matchedVehicle = $vehicles[$index];
                            }

                            if ($matchedVehicle) {
                                $vehicle = $matchedVehicle;
                                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Found vehicle by order data match', [
                                    'vehicleId' => $vehicleId,
                                    'matchType' => ($vehicleOrder['mopId'] ?? '') === $vehicleId ? 'mopId' : 'orderFormId',
                                    'mopId' => $vehicleOrder['mopId'] ?? '',
                                    'orderFormId' => $vehicleOrder['orderFormId'] ?? '',
                                    'vehicleVin' => $vehicleVin,
                                ]);
                                break;
                            }
                        }
                    }
                }
            }

            if (!$vehicle) {
                throw new \Exception('Vehicle not found', Response::HTTP_NOT_FOUND);
            }

            // Convert ODM Vehicle to legacy VehicleModel format for backward compatibility
            // Handle null values properly to prevent denormalization errors

            // Try to get order information from the ODM vehicle or legacy data
            $mopId = '';
            $orderFormId = '';
            $trackingStatus = '';
            $orderFormStatus = '';

            // Check if order information exists in the raw MongoDB document
            // The legacy system stored order data in vehicleOrder field
            $orderInfo = $this->vehicleOrderService->extractOrderInformationFromVehicle($vehicle, $userId);
            if ($orderInfo) {
                $mopId = $orderInfo['mopId'] ?? '';
                $orderFormId = $orderInfo['orderFormId'] ?? '';
                $trackingStatus = $orderInfo['trackingStatus'] ?? '';
                $orderFormStatus = $orderInfo['orderFormStatus'] ?? '';

                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Found order information for vehicle', [
                    'vehicleId' => $vehicleId,
                    'userId' => $userId,
                    'mopId' => $mopId,
                    'orderFormId' => $orderFormId,
                    'trackingStatus' => $trackingStatus,
                    'orderFormStatus' => $orderFormStatus,
                ]);
            } else {
                $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' No order information found for vehicle', [
                    'vehicleId' => $vehicleId,
                    'userId' => $userId,
                ]);
            }

            $vehicleArray = [
                'id' => $vehicle->getVin() ?? '', // Using VIN as ID
                'vin' => $vehicle->getVin() ?? '',
                'brand' => $vehicle->getBrand() ?? 'UNKNOWN', // Handle null brand
                'model' => $vehicle->getModel() ?? '', // Handle null model
                'versionId' => $vehicle->getVersionId() ?? '', // Handle null versionId
                'country' => '', // Will need to be set from user data or context
                'vehicleOrder' => [
                    'mopId' => $mopId,
                    'orderFormId' => $orderFormId,
                    'trackingStatus' => $trackingStatus,
                    'orderFormStatus' => $orderFormStatus,
                ]
            ];

            $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Converting ODM vehicle to VehicleModel', [
                'vehicleId' => $vehicleId,
                'userId' => $userId,
                'vehicleArray' => $vehicleArray,
            ]);

            return $this->denormalizer->denormalize($vehicleArray, VehicleModel::class);

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting vehicle info', [
                'vehicleId' => $vehicleId,
                'userId' => $userId,
                'exception' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
