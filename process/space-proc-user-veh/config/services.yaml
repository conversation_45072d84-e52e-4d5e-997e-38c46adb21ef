# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    # MongoDB ODM
    mongodb.url: "%env(MONGO_DB_URL)%"
    mongodb.db: "%env(MONGODB_DB)%"
    env(MONGODB_URL): ''
    env(MONGODB_DB): ''

    brands.xp: ['AP', 'AC', 'DS', 'OP', 'VX']
    brands.ssdp: ['JE', 'AR']

imports:
    - { resource: 'visual_settings.yaml' }

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    App\EventListener\ExceptionListener:
        tags: [kernel.event_listener]

    App\Connector\SystemOmniConnector:
        arguments:
            $url: "%env(MS_SYS_OMNI_URL)%"


    App\Connector\SystemIdpConnector:
        arguments:
            $url: "%env(MS_SYS_IDP_URL)%"

    App\Connector\SystemCorvetDataConnector:
        arguments:
            $url: "%env(MS_SYS_CORVET_DATA_URL)%"

    App\Connector\ProcessMeConnector:
        arguments:
            $url: "%env(MY_MARQUE_PROC_ME_URL)%"
            $apiKey: "%env(SPACE_MIDDLEWARE_API_KEY)%"
    
    App\Connector\SysSamsDataConnector:
        arguments:
            $url: "%env(MS_SYS_SAMS_DATA_URL)%"
              
    App\Helper\BrandHelper:
        arguments:
            $xpBrands: '%brands.xp%'
            $ssdpBrands: '%brands.ssdp%'

    App\Connector\SystemSdprConnector:
        arguments:
            $url: '%env(MS_SYS_SDPR_URL)%'

    App\Connector\SystemUserDataConnector:
        arguments:
            $url: '%env(MS_MYM1_SYS_USER_DATA_URL)%'
    
    App\Connector\SystemUserDBConnector:
        arguments:
            $url: '%env(MS_SYS_USER_DB_URL)%'

    App\MessageHandler\RefreshVehicleMessageHandler:
        tags: [messenger.aws_sqs_refresh_vehicle_add_vehicle]
    
    App\Service\VehicleVisualService:
        arguments:
            $mongoDBService: '@Space\MongoDocuments\Service\MongoDBService'
            $visualSettings: '%visual3d%'

    App\Service\FeatureCodeService:
        arguments:
            $cdnUrl: '%env(SETTINGS_CDN_URL)%'

    App\MessageHandler\ConsumerRightsMessageHandler:
        tags: [messenger.aws_sqs_consumer_rights]

    # Space MongoDB Documents Service
    Space\MongoDocuments\Service\MongoDBService:
        arguments:
            $documentManager: '@doctrine_mongodb.odm.document_manager'
            $logger: '@logger'

    # Space MongoDB Documents Repository
    Space\MongoDocuments\Repository\UserDataRepository:
        factory: ['@doctrine_mongodb.odm.document_manager', 'getRepository']
        arguments:
            - 'Space\MongoDocuments\Document\UserData'

    Space\MongoDocuments\Repository\VehicleLabelRepository:
        factory: ['@doctrine_mongodb.odm.document_manager', 'getRepository']
        arguments:
            - 'Space\MongoDocuments\Document\VehicleLabel'

    Space\MongoDocuments\Repository\SettingsRepository:
        factory: ['@doctrine_mongodb.odm.document_manager', 'getRepository']
        arguments:
            - 'Space\MongoDocuments\Document\Settings'

    # Vehicle Service Layer - Following microservice architecture patterns
    App\Service\VehicleMapperService:
        # No additional dependencies needed

    App\Service\VehicleDataService:
        arguments:
            $mongoDBService: '@Space\MongoDocuments\Service\MongoDBService'
            $userDataService: '@App\Service\UserDataService'
            $vehicleMapperService: '@App\Service\VehicleMapperService'
            $vehicleTransformationService: '@App\Service\VehicleTransformationService'
            $featureCodeService: '@App\Service\FeatureCodeService'
            $vehicleUtilityService: '@App\Service\VehicleUtilityService'

    App\Service\VehicleValidationService:
        arguments:
            $brandProvider: '@App\Helper\BrandProvider'

    App\Service\VehicleTransformationService:
        arguments:
            $validationService: '@App\Service\VehicleValidationService'
            $vehicleOrderService: '@App\Service\VehicleOrderService'
            $userDataService: '@App\Service\UserDataService'

    # Updated VehicleLabelService to use ODM
    App\Service\VehicleLabelService:
        arguments:
            $mongoDBService: '@Space\MongoDocuments\Service\MongoDBService'

    # Additional specialized services for VehicleManager refactoring
    App\Service\VehicleOrderService:
        arguments:
            $mongoDBService: '@Space\MongoDocuments\Service\MongoDBService'
            $userDataService: '@App\Service\UserDataService'

    App\Service\VehicleFeatureService:
        arguments:
            $featureCodeService: '@App\Service\FeatureCodeService'
            $userDataService: '@App\Service\UserDataService'
            $vehicleCorvetService: '@App\Service\VehicleCorvetService'

    App\Service\VehicleCorvetService: ~

    App\Service\VehicleUtilityService:
        arguments:
            $userDataService: '@App\Service\UserDataService'
            $denormalizer: '@serializer'
            $vehicleOrderService: '@App\Service\VehicleOrderService'
